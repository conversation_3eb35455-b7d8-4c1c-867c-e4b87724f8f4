package com.dg.wss.web.controller.all;

import com.dg.wss.all.domain.AllApiParams;
import com.dg.wss.all.service.IAllApiParamsService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 系统API入参列表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@RestController
@RequestMapping("/allApiParams")
public class AllApiParamsController extends BaseController {
    @Autowired
    private IAllApiParamsService allApiParamsService;


    /**
     * 查询系统API入参列表列表
     */
    @ApiOperation(value = "查询系统API入参列表列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo list(@ModelAttribute AllApiParams allApiParams) {
        fillTime(allApiParams);
        startPage();
        List<AllApiParams> list = allApiParamsService.listAllApiParams(allApiParams);
        return getDataTable(list);
    }

    /**
     * 查询系统API入参列表详情
     */
    @ApiOperation(value = "查询系统API入参列表详情")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult list(@RequestParam Long id) {
        return AjaxResult.success(allApiParamsService.getById(id));
    }

    /**
    * 新增系统API入参列表信息
    */
    @ApiOperation(value = "新增系统API入参列表信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统API入参列表", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@RequestBody AllApiParams allApiParams){
        allApiParams.setCreateBy(getUserId());
//        allApiParams.setCircleId(getCircleId());
        allApiParams.setCreateTime(new Date());
        return AjaxResult.success(allApiParamsService.save(allApiParams));
    }

    /**
    * 批量保存系统API入参列表
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统API入参列表", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAdd")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchAdd(@RequestBody List<AllApiParams> allApiParamsList) {
        for (AllApiParams allApiParams : allApiParamsList) {
//            allApiParams.setCircleId(getCircleId());
            allApiParams.setCreateBy(getUserId());
            allApiParams.setCreateTime(new Date());
        }
        return AjaxResult.success(allApiParamsService.saveBatch(allApiParamsList));
    }

    /**
     * 修改系统API入参列表信息
     */
    @ApiOperation(value = "修改系统API入参列表信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统API入参列表", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@RequestBody AllApiParams allApiParams){
//        allApiParams.setUpdateBy(getUserId());
//        allApiParams.setCircleId(getCircleId());
//        allApiParams.setUpdateTime(new Date());
        return AjaxResult.success(allApiParamsService.updateById(allApiParams));
    }

    /**
    * 批量修改系统API入参列表
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统API入参列表", businessType = BusinessType.UPDATE)
    @PutMapping("/batchEdit")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchEdit(@RequestBody List<AllApiParams> allApiParamsList) {
        for (AllApiParams allApiParams : allApiParamsList) {
//            allApiParams.setCircleId(getCircleId());
//            allApiParams.setUpdateBy(getUserId());
//            allApiParams.setUpdateTime(new Date());
        }
        return AjaxResult.success(allApiParamsService.updateBatchById(allApiParamsList));
    }

    /**
     * 删除系统API入参列表信息
     */
    @ApiOperation(value = "删除系统API入参列表信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统API入参列表", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable String id){
        return AjaxResult.success(allApiParamsService.removeById(id));
    }
}