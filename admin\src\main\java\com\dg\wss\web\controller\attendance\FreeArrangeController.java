package com.dg.wss.web.controller.attendance;

import com.dg.wss.attendance.domain.FreeArrange;
import com.dg.wss.attendance.service.IFreeArrangeService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Api(tags = "考勤自由班制")
@RestController
@RequestMapping("/attendance/freeArrange")
public class FreeArrangeController extends BaseController {
    @Autowired
    private IFreeArrangeService freeArrangeService;

    @ApiOperation(value = "添加考勤自由班制")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime"})
    @DynamicResponseParameters(name = "freeArrange_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/add")
    @RepeatSubmit
    @Log(title = "考勤自由班", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated @RequestBody FreeArrange freeArrange) {
        freeArrange.setCreateBy(getUserId());
        synchronized (this) {
            Long id = freeArrangeService.addFreeArrange(freeArrange);
            return id == null ? AjaxResult.error() : AjaxResult.success(id);
        }
    }

    @ApiOperation(value = "编辑考勤自由班制")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime"})
    @DynamicResponseParameters(name = "freeArrange_edit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/edit")
    @RepeatSubmit
    @Log(title = "考勤自由班", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated @RequestBody FreeArrange freeArrange) {
        synchronized (this) {
            if (freeArrange.getUserId() != null) freeArrange.setCircleId(getCircleId());
            return toAjax(freeArrangeService.editFreeArrange(freeArrange));
        }
    }

    @ApiOperation(value = "删除考勤自由班制")
    @DynamicResponseParameters(name = "freeArrange_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @DeleteMapping("/remove")
    @RepeatSubmit
    @Log(title = "考勤自由班", businessType = BusinessType.DELETE)
    public AjaxResult delete(@Validated Long id) {
        return toAjax(freeArrangeService.deleteFreeArrange(id));
    }

    @ApiOperation(value = "id查询")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = FreeArrange.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("id") @RequestParam Long id) {
        return AjaxResult.success(freeArrangeService.getInfo(id));
    }
}
