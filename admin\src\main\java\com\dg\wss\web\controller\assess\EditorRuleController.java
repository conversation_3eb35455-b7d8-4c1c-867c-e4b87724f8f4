package com.dg.wss.web.controller.assess;

import com.dg.wss.assess.domain.EditorRule;
import com.dg.wss.assess.service.IEditorRuleService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 编辑任务规则
 */
@Api(tags = "编辑任务规则")
@RestController
@RequestMapping("/assess/editorRule")
public class EditorRuleController extends BaseController {
    @Autowired
    private IEditorRuleService editorRuleService;

    @ApiOperation(value = "添加编辑任务规则")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "editorRule_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "编辑任务规则", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('assess:editorRule:add')")
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody EditorRule editorRule) {
        try {
            editorRule.setCreateBy(getUsername());
            return toAjax(editorRuleService.insertEditorRule(editorRule));
        } catch (SQLException e) {
            logger.error("编辑任务规则添加失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "修改编辑任务规则")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "editorRule_edit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "编辑任务规则", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('assess:editorRule:edit')")
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@Validated @RequestBody EditorRule editorRule) {
        try {
            editorRule.setUpdateBy(getUsername());
            return toAjax(editorRuleService.updateEditorRule(editorRule));
        } catch (SQLException e) {
            logger.error("修改编辑任务规则失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "Sting")})
    @DynamicResponseParameters(name = "editorRule_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = EditorRule[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> map) {
        startPage();
        List<EditorRule> list = editorRuleService.selectEditorRuleList(map);
        return getDataTable(list);
    }

}
