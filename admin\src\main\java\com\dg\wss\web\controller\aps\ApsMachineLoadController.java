package com.dg.wss.web.controller.aps;

import com.dg.wss.aps.domain.ApsMachine;
import com.dg.wss.aps.service.IApsMachineLoadService;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.commonservice.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

@Api(tags = "aps机荷")
@RestController
@RequestMapping("/aps/apsMachineLoad")
public class ApsMachineLoadController extends BaseController {
    @Autowired
    private IApsMachineLoadService machineLoadService;

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        TableDataInfo tableDataInfo = machineLoadService.pageByMap(map);
        return tableDataInfo;
    }

    @ApiOperation(value = "查询机器实例的负荷")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listCustomDevice")
    public TableDataInfo listCustomDevice(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        return getDataTable(machineLoadService.listCustomDevice(map));
    }


}