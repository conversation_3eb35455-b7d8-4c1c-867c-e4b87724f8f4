package com.dg.wss.web.controller.attendance;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.dg.wss.attendance.domain.AttendanceDevice;
import com.dg.wss.attendance.domain.DeviceFailUser;
import com.dg.wss.attendance.domain.DeviceUser;
import com.dg.wss.attendance.service.*;
import com.dg.wss.common.constant.RedisConstants;
import com.dg.wss.common.core.redis.RedisCache;
import com.dg.wss.common.utils.StringUtils;
import com.dg.wss.common.vo.device.request.*;
import com.dg.wss.common.vo.device.request.item.FailEmployeeVo;
import com.dg.wss.common.vo.device.response.DeleteEmployeeIdVo;
import com.dg.wss.common.vo.device.response.DeviceRestartVo;
import com.dg.wss.common.vo.device.response.DeviceSetVo;
import com.dg.wss.common.vo.device.response.ExportEmployeeVo;
import com.dg.wss.common.vo.device.response.item.DeviceParameterVo;
import com.dg.wss.common.vo.device.response.item.EmployeeIdVo;
import com.dg.wss.common.vo.device.response.item.PassInfoVo;
import com.dg.wss.common.vo.device2.RecordFace;
import com.dg.wss.common.vo.device2.RecordFaceLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "考勤机同步接口")
@RestController
public class DeviceSyncController {
    @Autowired
    private IAttendanceDeviceService attendanceDeviceService;
    @Autowired
    private IGroupDeviceService groupDeviceService;
    @Autowired
    private IAttendanceClockService attendanceClockService;
    @Autowired
    private IDeviceUserService deviceUserService;
    @Autowired
    private IDeviceFailUserService deviceFailUserService;
    @Resource
    RedisCache redisCache;

    @ApiOperation(value = "新设备打卡")
    @PostMapping("api/v1/record/face")
    public Map<String, Object> face(@RequestBody RecordFace recordFace) {
        log.debug("录入人员打卡：" + JSONUtil.toJsonStr(recordFace));
        if (CollUtil.isNotEmpty(recordFace.getLogs())) {
            for (RecordFaceLog log : recordFace.getLogs()) {
                if ("face".equals(log.getRecog_type())) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("userId", log.getUser_id());
                    map.put("deviceId", recordFace.getSn());
                    map.put("dateStr", log.getRecog_time());
                    redisCache.leftPush(RedisConstants.RDS_ATTENDANCE_CLOCK, map);//队列打卡
                }
            }
        }
        return getDefault2();//0成功。设备收到返回为0后，就会删除设备本地的记录。
    }

    @ApiOperation(value = "新设备陌生人记录")
    @PostMapping("api/v1/stranger")
    public Map<String, Object> stranger(@RequestBody RecordFace recordFace) {
        log.info("陌生人打卡：" + JSONUtil.toJsonStr(recordFace));
        return getDefault2();
    }

    //旧设备
    @ApiOperation(value = "设备主动往云端实时上传打卡记录")
    @PostMapping("note/insertNoteFace")
    public Map<String, Object> insertNoteFace(@RequestBody NoteFaceVo noteFace) {
        if ("-1".equals(noteFace.getEmployeeId()) || StringUtils.isEmpty(noteFace.getEmployeeId())) // 非录入人员
            log.debug("非录入人员打卡：" + JSONUtil.toJsonStr(noteFace));
        else {
            if (noteFace.getNotePass() == null || noteFace.getNotePass() != 0) {// 打卡
                if (noteFace.getDeviceId().length() > 20) {//手机考勤机打卡
                    Map<String, Object> map = new HashMap<>();
                    map.put("userId", noteFace.getEmployeeId());
                    map.put("deviceId", noteFace.getDeviceId());
                    map.put("dateStr", noteFace.getNoteTime());
                    redisCache.leftPush(RedisConstants.RDS_ATTENDANCE_CLOCK, map);//队列打卡
                } else //考勤机
                    attendanceClockService.addClock(noteFace);
            } else
                log.debug("打卡未通过：" + JSONUtil.toJsonStr(noteFace));// 打卡未通过
        }
        return getDefault();
    }

    @ApiOperation(value = "设备主动触发云端导入雇员授权信息")
    @PostMapping("devicePass/selectPassInfo")
    public ExportEmployeeVo selectPassInfo(@RequestBody ExportPassVo exportPass) {
        log.debug("导入员工：" + JSONUtil.toJsonStr(exportPass));
        ExportEmployeeVo exportEmployee = new ExportEmployeeVo();
        Map<String, Object> tempMap = redisCache.getCacheMapValue(RedisConstants.RDS_DEVICE_USER, exportPass.getDeviceId());
        if (tempMap != null) {
            if (!tempMap.containsKey("isDelete") && tempMap.containsKey("userIds")) {// 已删除
                List<Long> idList = (List<Long>) tempMap.get("userIds");
                List<PassInfoVo> passInfoList;
                if (idList.size() > 50) {
                    passInfoList = groupDeviceService.selectUser(exportPass.getDeviceId(), CollectionUtil.sub(idList, 0, 50), 50);// 超过50个，分批处理
                    tempMap.put("userIds", CollectionUtil.sub(idList, 50, idList.size()));
                    redisCache.setCacheMapValue(RedisConstants.RDS_DEVICE_USER, exportPass.getDeviceId(), tempMap);
                } else {
                    passInfoList = groupDeviceService.selectUser(exportPass.getDeviceId(), idList, 50);// 超过50个，分批处理
                    redisCache.deleteCacheMap(RedisConstants.RDS_DEVICE_USER, exportPass.getDeviceId());
                }
                exportEmployee.setPassInfo(passInfoList);
            }
        } else {
            DeviceUser deviceUser = deviceUserService.selectById(exportPass.getDeviceId());
            if (deviceUser != null && CollectionUtil.isNotEmpty(deviceUser.getAddIds())) {
                List<PassInfoVo> passInfoList = groupDeviceService.selectUser(exportPass.getDeviceId(), deviceUser.getAddIds(), 50);// 超过50个，分批处理
                if (!passInfoList.isEmpty()) {
                    for (PassInfoVo item : passInfoList) {
                        Long userId = Long.parseLong(item.getEmployeeId());
                        deviceUser.getAdding().add(userId);
                        deviceUser.getAddIds().remove(userId);
                    }
                    deviceUser.setAdding(deviceUser.getAdding().stream().distinct().collect(Collectors.toList()));
                    deviceUserService.editDeviceUser(deviceUser);
                    exportEmployee.setPassInfo(passInfoList);
                }
            }
        }
        return exportEmployee;
    }

    @ApiOperation(value = "导入雇员授权信息结果反馈")
    @PostMapping("devicePass/setPassResult")
    public Map<String, Object> setPassResult(@RequestBody ExportResultVo exportResult) {
        log.debug("导入员工结果：" + JSONUtil.toJsonStr(exportResult));
        List<Long> failIds = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(exportResult.getFailEmployeeId())) {// 修改上次添加时间
            deviceFailUserService.addFailUser(new DeviceFailUser(exportResult.getDeviceId(), exportResult.getFailEmployeeId()));// 记录失败人员
            for (FailEmployeeVo item : exportResult.getFailEmployeeId()) {
                failIds.add(Long.parseLong(item.getEmployeeId()));
            }
        }
        if ((exportResult.getSuccessNumber() != null && exportResult.getSuccessNumber() > 0) || !failIds.isEmpty()) {
            DeviceUser deviceUser = deviceUserService.selectById(exportResult.getDeviceId());
            if (deviceUser != null && CollectionUtil.isNotEmpty(deviceUser.getAdding())) {
                for (Long id : deviceUser.getAdding()) {
                    if (failIds.indexOf(id) == -1)
                        deviceUser.getUsers().add(id);// 成功的用户
                }
                deviceUser.setAdding(new ArrayList<>());
                deviceUser.setUsers(deviceUser.getUsers().stream().distinct().collect(Collectors.toList()));
                deviceUser.getAddIds().addAll(failIds);// 失败放回列表，下次添加
                deviceUser.setAddIds(deviceUser.getAddIds().stream().distinct().collect(Collectors.toList()));
                deviceUserService.editDeviceUser(deviceUser);
            }
        }
        return getDefault();
    }

    @ApiOperation(value = "设备主动触发云端获取删除雇员信息")
    @PostMapping("devicePass/selectDeleteInfo")
    public DeleteEmployeeIdVo selectDeleteInfo(@RequestBody OnlyDeviceIdVo deviceId) {
        log.debug("删除雇员：" + JSONUtil.toJsonStr(deviceId));
        DeleteEmployeeIdVo deleteEmployeeId = new DeleteEmployeeIdVo();
        DeviceUser deviceUser = deviceUserService.selectById(deviceId.getDeviceId());
        boolean isAll = false;
        if (deviceUser != null && CollectionUtil.isNotEmpty(deviceUser.getDelIds())) {
            List<EmployeeIdVo> list = new ArrayList<>();
            for (Long id : deviceUser.getDelIds()) {
                if (Objects.equals(0L, id)) {// 里面存入0时，一般为修改圈子ID，所以要全清
                    isAll = true;
                    break;
                }
                list.add(new EmployeeIdVo(id + ""));
                deviceUser.getUsers().remove(id);// 从已添加里面删除对应人员
            }
            if (isAll) {// 全删
                deleteEmployeeId.setEmpty(1);
                deviceUser.setUsers(new ArrayList<>());// 清空已添加列表
                deviceUser.setAddIds(new ArrayList<>());// 清空待添加列表
                deviceUser.setAdding(new ArrayList<>());// 清空同步中列表
            } else {
                deleteEmployeeId.setEmpty(0);
                deleteEmployeeId.setDeleteInfo(list);
            }
            deviceUser.setDelIds(new ArrayList<>());// 清空删除列表
            deviceUserService.editDeviceUser(deviceUser);
        }
        Map<String, Object> tempMap = redisCache.getCacheMapValue(RedisConstants.RDS_DEVICE_USER, deviceId.getDeviceId());
        if (tempMap != null) {
            if (isAll) {
                redisCache.deleteCacheMap(RedisConstants.RDS_DEVICE_USER, deviceId.getDeviceId());
            } else {// 不为全删
                if (tempMap.containsKey("isDelete") && tempMap.containsKey("userIds")) {// 删除
                    List<Long> idList = (List<Long>) tempMap.get("userIds");
                    if (deleteEmployeeId.getDeleteInfo() == null)
                        deleteEmployeeId.setDeleteInfo(new ArrayList<>());
                    for (Long id : idList) {
                        deleteEmployeeId.getDeleteInfo().add(new EmployeeIdVo(id + ""));
                    }
                    tempMap.remove("isDelete");
                    redisCache.setCacheMapValue(RedisConstants.RDS_DEVICE_USER, deviceId.getDeviceId(), tempMap);
                }
            }
        }
        return deleteEmployeeId;
    }

    @ApiOperation(value = "设备上传当前设备初始参数信息")
    @PostMapping("parameter/inertParameter")
    public Map<String, Object> inertParameter(@RequestBody DeviceInfoVo deviceInfo) {
        log.debug("初始参数：" + JSONUtil.toJsonStr(deviceInfo));
        AttendanceDevice device = new AttendanceDevice(deviceInfo);
        device.setParameterfirmwaremsg(deviceInfo.getParameterFirmwareMsg());
        attendanceDeviceService.initParam(device);
        if (is5inch(deviceInfo.getDeviceId())) {//8寸固件不通用
            Map<String, String> deviceMap = new HashMap<>();
            deviceMap.put("ver", deviceInfo.getParameterFirmwareMsg());
            deviceMap.put("isSend", "0");
            redisCache.setCacheMapValue(RedisConstants.DEVICE_VER, deviceInfo.getDeviceId(), deviceMap);
        }
        return getDefault();
    }

    //    @Value("${attendanceDevice.ver}")
    private String ver = "V3.0-20240801";
    //    @Value("${attendanceDevice.url}")
    private String url = "https://wensanshi-dev.oss-cn-shenzhen.aliyuncs.com/app/EUPGRADE-4bedc45ddefee1bd9ec1120e24f1368b.efw";
    //    @Value("${attendanceDevice.md5}")
    private String md5 = "4bedc45ddefee1bd9ec1120e24f1368b";

    @ApiOperation(value = "设备主动触发云端获取设备需要设置的参数信息")
    @PostMapping("parameter/selectParameterInfo")
    public DeviceSetVo selectParameterInfo(@RequestBody OnlyDeviceIdVo deviceId) {
        log.debug("设置参数：" + JSONUtil.toJsonStr(deviceId));
        DeviceSetVo deviceSet = new DeviceSetVo();
        DeviceParameterVo deviceParameter = new DeviceParameterVo(30);// 防止修改，每次都设置为轮训时间30秒
        if (is5inch(deviceId.getDeviceId())) {
            Map<String, String> deviceMap = redisCache.getCacheMapValue(RedisConstants.DEVICE_VER, deviceId.getDeviceId());
            if (deviceMap != null && "0".equals(deviceMap.get("isSend")) && !ver.equals(deviceMap.get("ver"))) {
                deviceParameter.setParameterFirmwarePath(url);
                deviceParameter.setParameterFirmwareMd5(md5);
                deviceParameter.setParameterFirmwareMsg(ver);
                deviceMap.put("isSend", "1");
                redisCache.setCacheMapValue(RedisConstants.DEVICE_VER, deviceId.getDeviceId(), deviceMap);
            }
        }
        deviceSet.setParameterInfo(deviceParameter);
        return deviceSet;
    }

    @ApiOperation(value = "验证设备在线状态")
    @PostMapping("device/updateStateDevice")
    public Map<String, Object> selectParameterInfo(@RequestBody CheckDeviceOnlineVo checkDeviceOnline) {
        log.debug("验证设备：" + JSONUtil.toJsonStr(checkDeviceOnline));
        attendanceDeviceService.deviceHeart(new AttendanceDevice(checkDeviceOnline.getDeviceId()));
        return getDefault();
    }

    @ApiOperation(value = "设备请求服务器特殊管理(重启、恢复出厂设置)")
    @PostMapping("device/selectRestart")
    public DeviceRestartVo selectRestart(@RequestBody OnlyDeviceIdVo deviceId) {
        log.debug("特殊管理：" + JSONUtil.toJsonStr(deviceId));
        return new DeviceRestartVo();
    }

    @ApiOperation(value = "设备更新服务器路径结果反馈")
    @PostMapping("device/setPathNews")
    public Map<String, Object> setPathNews(@RequestBody UpdatePathResultVo updatePathResult) {
        log.debug("更新服务器结果：" + JSONUtil.toJsonStr(updatePathResult));
        return getDefault();
    }

    private Map<String, Object> getDefault() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", 0);
        return result;
    }

    private Map<String, Object> getDefault2() {
        Map<String, Object> result = new HashMap<>();
        result.put("Result", 0);
        result.put("Msg", "");
        return result;
    }

    private boolean is5inch(String deviceId) {//判断是5寸机，设备ID不太规范，5寸有SKY0523xxxxxx和SKY518202400xx两种格式，8寸目前只有一台是SKY2023111001
        return !deviceId.startsWith("SKY20");
    }
}
