package com.dg.wss.web.controller.assess;

import com.dg.wss.assess.domain.ProjectRule;
import com.dg.wss.assess.service.IProjectRuleService;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.doc.entity.Project;
import com.dg.wss.doc.service.IProjectService;
import com.dg.wss.work.domain.Expert;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Api(tags = "项目任务规则")
@RestController
@RequestMapping("/assess/projectRule")
public class ProjectRuleController extends BaseController {
    @Autowired
    private IProjectRuleService projectRuleService;
    @Autowired
    private IProjectService projectService;

    @ApiOperation(value = "添加项目任务规则")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "projectRule_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasPermi('assess:userLevel:add')")
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody ProjectRule projectRule) {
        return toAjax(projectRuleService.insertProjectRule(projectRule, getUserId()));
    }

    @ApiOperation(value = "添加多个项目任务规则")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "projectRule_add_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasPermi('assess:projectRule:add')")
    @PostMapping("/addlist")
    public AjaxResult addList(@RequestBody List<ProjectRule> projectRuleList) {
        return toAjax(projectRuleService.insertMultProjectRule(projectRuleList, getUserId()));
    }

    @ApiOperation(value = "修改项目任务规则")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "projectRule_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasPermi('assess:userLevel:edit')")
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@Validated @RequestBody ProjectRule projectRule) {
        return toAjax(projectRuleService.updateProjectNeeds(projectRule, getUserId()));
    }

    @ApiOperation(value = "分页查询项目任务规则")
    @PreAuthorize("@ss.hasRole('basics')")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "typeId", value = "项目类型", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "userId", value = "用户名字", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "expertId", value = "专家id", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "decimal"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "decimal")})
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ProjectRule.class)})
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<ProjectRule> list = projectRuleService.selectProjectRuleList(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "查询项目任务规则 id")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ProjectRule.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("项目任务规则ID") @RequestParam Long id) {
        return AjaxResult.success(projectRuleService.selectById(id));
    }

    @ApiOperation(value = "删除项目任务规则")
    @DynamicResponseParameters(name = "projectRule_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasPermi('assess:projectRule:remove')")
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("项目任务规则ID") @PathVariable Long id) {
        return toAjax(projectRuleService.deleteProjectRule(id));
    }

    @ApiOperation(value = "查询项目可分配专家列表")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = Expert.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/selectExpertByCity")
    public AjaxResult selectExpertByCity(@ApiParam("ESID") @RequestParam String esId) {
        return AjaxResult.success(projectRuleService.selectExpertByCity(esId));
    }

    @ApiOperation(value = "给项目重新分配专家")
    @ApiOperationSupport(includeParameters = {"id", "expertId", "expertName"})
    @DynamicResponseParameters(name = "projectRule_add_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasPermi('assess:projectRule:distributionExpert')")
    @PutMapping("/distributionExpert")
    public AjaxResult distributionExpert(@RequestBody Project project) {
        try {
            return toAjax(projectService.distributionExpert(project));
        } catch (IOException e) {
            logger.error("重新分配专家异常：", e);
            return AjaxResult.error("重新分配专家异常");
        }
    }
}
