package com.dg.wss.web.controller.assess;

import com.dg.wss.assess.domain.Task;
import com.dg.wss.assess.service.ITaskService;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

@Api(tags = "工作任务")
@RestController
@RequestMapping("/assess/task")
public class TaskController extends BaseController {

    @Autowired
    private ITaskService taskService;

    @ApiOperation(value = "添加工作任务")
    @ApiOperationSupport(ignoreParameters = {"id", "createTime", "userId"})
    @DynamicResponseParameters(properties = {
            @DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@RequestBody Task task) {
        return toAjax(taskService.insertTask(task));
    }

    @ApiOperation(value = "修改工作任务")
    @ApiOperationSupport(ignoreParameters = {"updateTime", "createTime", "userId"})
    @DynamicResponseParameters(properties = {
            @DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@RequestBody Task task) {
        return toAjax(taskService.updateTask(task));
    }

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "title", value = "任务名称", dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "订单id", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "datetime"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "datetime")})
    @DynamicResponseParameters(name = "task_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = Task[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<Task> list = taskService.selectTaskList(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "id查询")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = Task.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("id") @RequestParam Long id) {
        return AjaxResult.success(taskService.getTaskById(id));
    }

    @ApiOperation(value = "删除工作任务")
    @ApiOperationSupport(ignoreParameters = {"type", "title", "esId", "createTime", "deadline", "status", "userId", "updateTime"})
    @DynamicResponseParameters(properties = {
            @DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @DeleteMapping("/remove")
    public AjaxResult remove(@RequestBody Task task) {
        return toAjax(taskService.deleteTask(task));
    }
}
