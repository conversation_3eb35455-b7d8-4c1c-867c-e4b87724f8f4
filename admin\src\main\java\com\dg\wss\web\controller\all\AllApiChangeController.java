package com.dg.wss.web.controller.all;

import com.dg.wss.all.domain.AllApiChange;
import com.dg.wss.all.service.IAllApiChangeService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 系统api变更记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-28
 */
@RestController
@RequestMapping("/all/allApiChange")
public class AllApiChangeController extends BaseController {
    @Autowired
    private IAllApiChangeService allApiChangeService;


    /**
     * 查询系统api变更记录列表
     */
    @ApiOperation(value = "查询系统api变更记录列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo list(@ModelAttribute AllApiChange allApiChange) {
        fillTime(allApiChange);
        startPage();
        List<AllApiChange> list = allApiChangeService.listAllApiChange(allApiChange);
        return getDataTable(list);
    }

    /**
     * 查询系统api变更记录详情
     */
    @ApiOperation(value = "查询系统api变更记录详情")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult list(@RequestParam Long id) {
        return AjaxResult.success(allApiChangeService.getById(id));
    }

    /**
    * 新增系统api变更记录信息
    */
    @ApiOperation(value = "新增系统api变更记录信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统api变更记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@RequestBody AllApiChange allApiChange){
        allApiChange.setCreateBy(getUserId());
        allApiChange.setCreateTime(new Date());
        return AjaxResult.success(allApiChangeService.save(allApiChange));
    }

    /**
    * 批量保存系统api变更记录
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统api变更记录", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAdd")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchAdd(@RequestBody List<AllApiChange> allApiChanges) {
        for (AllApiChange allApiChange : allApiChanges) {
            allApiChange.setCreateBy(getUserId());
            allApiChange.setCreateTime(new Date());
        }
        return AjaxResult.success(allApiChangeService.saveBatch(allApiChanges));
    }

    /**
     * 修改系统api变更记录信息
     */
    @ApiOperation(value = "修改系统api变更记录信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统api变更记录", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@RequestBody AllApiChange allApiChange){
        return AjaxResult.success(allApiChangeService.updateById(allApiChange));
    }

    /**
    * 批量修改系统api变更记录
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统api变更记录", businessType = BusinessType.UPDATE)
    @PutMapping("/batchEdit")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchEdit(@RequestBody List<AllApiChange> allApiChanges) {
        return AjaxResult.success(allApiChangeService.updateBatchById(allApiChanges));
    }

    /**
     * 删除系统api变更记录信息
     */
    @ApiOperation(value = "删除系统api变更记录信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统api变更记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable String id){
        return AjaxResult.success(allApiChangeService.removeById(id));
    }
}
