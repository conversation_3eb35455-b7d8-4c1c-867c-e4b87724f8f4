package com.dg.wss.web.controller.base;

import com.dg.wss.base.domain.Sensitive;
import com.dg.wss.base.service.ISensitiveService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "敏感词")
@RestController
@RequestMapping("/base/sensitive")
public class SensitiveController extends BaseController {

    @Autowired
    private ISensitiveService sensitiveService;

    @ApiOperation(value = "添加")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "敏感词管理", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('base:sensitive:add')")
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody Sensitive sensitive) {
        try {
            sensitive.setCreateBy(getUsername());
            return toAjax(sensitiveService.insertSensitive(sensitive));
        } catch (Exception e) {
            logger.error("铭感词添加异常", e);
            return error(e.getMessage());
        }
    }

    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "敏感词管理", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('base:sensitive:edit')")
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@Validated @RequestBody Sensitive sensitive) {
        sensitive.setUpdateBy(getUsername());
        return toAjax(sensitiveService.updateSensitive(sensitive));
    }

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", name = "text", value = "敏感文字", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "状态", dataType = "Long")})
    @DynamicResponseParameters(name = "Sensitive_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = Sensitive[].class)})
    @PreAuthorize("@ss.hasPermi('base:sensitive:list')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> map) {
        startPage();
        List<Sensitive> list = sensitiveService.selectSensitiveList(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "id查询")
    @DynamicResponseParameters(name = "Sensitive_getInfo", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = Sensitive.class)})
    @PreAuthorize("@ss.hasPermi('base:sensitive:getInfo')")
    @GetMapping("/getInfo")
    public AjaxResult getInfo(Long id) {
        return AjaxResult.success(sensitiveService.getSensitiveById(id));
    }

    @ApiOperation(value = "删除多个敏感词")
    @Log(title = "敏感词管理", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('base:sensitive:remove')")
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long[] id) {
        return id.length > 0 ? toAjax(sensitiveService.deleteSensitive(id)) : AjaxResult.error();
    }
}
