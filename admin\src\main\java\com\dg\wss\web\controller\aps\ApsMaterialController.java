package com.dg.wss.web.controller.aps;

import com.dg.wss.aps.domain.ApsMaterial;
import com.dg.wss.aps.service.IApsMaterialService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.crm.domain.CrmProduct;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Api(tags = "aps领料")
@RestController
@RequestMapping("/aps/apsMaterial")
public class ApsMaterialController extends BaseController {
    @Autowired
    private IApsMaterialService apsMaterialService;

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        List<ApsMaterial> list = apsMaterialService.pageByMap(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listByTaskId")
    public TableDataInfo getListByTaskId(@ApiIgnore @RequestParam Long taskId, @RequestParam(required = false) Integer materialType) {
        startPage();
        List<ApsMaterial> list = apsMaterialService.getListByTaskId(taskId,materialType);
        return getDataTable(list);
    }

    @ApiOperation(value = "查询任务物料线边仓库存情况")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInventorysByTaskId")
    public TableDataInfo getInventorysByTaskId(@ApiIgnore @RequestParam Long taskId,
        Integer pageNum,
        Integer pageSize) {
        List<CrmProduct> list = apsMaterialService.getInventorysByTaskId(taskId, pageNum, pageSize);
        return getDataTable(list);
    }

    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps令单", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ApsMaterial apsMaterial) {
        apsMaterial.setCreateBy(getUserId());
        apsMaterial.setCreateTime(new Date());
        return toAjax(apsMaterialService.insert(apsMaterial, getLoginUser().getUser()));
    }

    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps令单", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/insertMaterial")
    public AjaxResult insertMaterialByOwnerId(@RequestBody ApsMaterial apsMaterial) {
        return AjaxResult.success(apsMaterialService.insertMaterialByOwnerId(apsMaterial.getBatchOrderId(),
                apsMaterial.getType(), getLoginUser().getUser()));
    }

    @ApiOperation(value = "批量添加")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps令单", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/batchAdd")
    public AjaxResult batchAdd(@RequestBody List<ApsMaterial> apsMaterials) {
        return toAjax(apsMaterialService.batchInsert(apsMaterials, getLoginUser().getUser()));
    }

    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps令单", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody ApsMaterial apsMaterial) {
        apsMaterial.setUpdateBy(getUserId());
        apsMaterial.setUpdateTime(new Date());
        return toAjax(apsMaterialService.update(apsMaterial));
    }

    @ApiOperation(value = "查询by id")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(apsMaterialService.selectById(id));
    }

    @ApiOperation(value = "删除")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps令单", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(apsMaterialService.delete(id));
    }

    @ApiOperation(value = "查询by batchOrderId")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getMaterial")
    public AjaxResult getMaterialByBatchOrderId(@RequestParam Long ownerId,
                                                @RequestParam Integer type,
                                                @RequestParam(required = false) String productType) {
        return AjaxResult.success(apsMaterialService.getMaterialByOwnerId(ownerId, type, productType, getLoginUser().getUser()));
    }

}