package com.dg.wss.web.controller.aps.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ResourceTaskVo {
    private String id;
    private String parent;
    private String name;
    private String scanCardRequire;
    private String qcRequire;
    private String outsource;
    private String apsTaskResourceList;
    private Integer machineTime;
    private Integer manTime;
    private BigDecimal num;
    private BigDecimal completeNum;
    private BigDecimal yield;
    private BigDecimal itemExceptionNum;
    private BigDecimal qcNgNum;
    private String processNote;
    private String p3;
    private String status;
    private String productCode;
    private String productName;
    private String moCode;
    private String startTime;
    private String endTime;
    private String color;

}
