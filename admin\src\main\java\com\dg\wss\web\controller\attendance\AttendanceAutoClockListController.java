package com.dg.wss.web.controller.attendance;

import com.dg.wss.attendance.domain.vo.AttendanceAutoClockListVo;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.utils.bean.BeanUtils;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.common.utils.StringUtils;
import com.dg.wss.commonservice.ExcelUtil;
import com.dg.wss.system.service.ISysUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestBody;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.attendance.domain.AttendanceAutoClockList;
import com.dg.wss.attendance.service.IAttendanceAutoClockListService;
import com.dg.wss.common.core.domain.AjaxResult;



/**
 * 自动打卡清单Controller
 *
 * <AUTHOR>
 * @date 2024-12-12
 */
@Controller
@Api(tags = "自动打卡清单")
@RequestMapping("/attendance/autoClockList")
public class AttendanceAutoClockListController extends BaseController
{
    @Autowired
    private IAttendanceAutoClockListService attendanceAutoClockListService;

    @Autowired
    private ISysUserService sysUserService;

    /**
     * 查询自动打卡清单列表
     */
    @ApiOperation(value = "查询自动打卡清单列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(@RequestBody AttendanceAutoClockList attendanceAutoClockList)
    {
        startPage();
        List<AttendanceAutoClockList> list = attendanceAutoClockListService.selectAttendanceAutoClockListList(attendanceAutoClockList);
        List<AttendanceAutoClockListVo> retList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(attendanceAutoClockList1 -> {
                AttendanceAutoClockListVo vo = new AttendanceAutoClockListVo();
                BeanUtils.copyProperties(attendanceAutoClockList1, vo);
                vo.setUserName(sysUserService.getNickName(attendanceAutoClockList1.getUserId()));
                retList.add(vo);
            });
        }

        return getDataTable(retList);
    }

    /**
     * 查询自动打卡清单
     */
    @ApiOperation(value = "查询自动打卡清单")
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/getById")
    @ResponseBody
    public AjaxResult getById(@RequestParam(name = "id",required = true) Long id)
    {
        return AjaxResult.success(attendanceAutoClockListService.selectAttendanceAutoClockListById(id));
    }

    /**
     * 导出自动打卡清单列表
     */
    //@RequiresPermissions("attendance:AutoClockList:export")
    @ApiOperation(value = "导出自动打卡清单列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "自动打卡清单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(AttendanceAutoClockList attendanceAutoClockList)
    {
        List<AttendanceAutoClockList> list = attendanceAutoClockListService.selectAttendanceAutoClockListList(attendanceAutoClockList);
        ExcelUtil<AttendanceAutoClockList> util = new ExcelUtil<AttendanceAutoClockList>(AttendanceAutoClockList.class);
        return util.exportExcel(list, "自动打卡清单数据");
    }

    /**
     * 新增保存自动打卡清单
     */
    @ApiOperation(value = "新增保存自动打卡清单")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "自动打卡清单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody AttendanceAutoClockList attendanceAutoClockList)
    {
        return toAjax(attendanceAutoClockListService.insertAttendanceAutoClockList(attendanceAutoClockList));
    }

    /**
     * 修改保存自动打卡清单
     */
    @ApiOperation(value = "修改保存自动打卡清单")
    //@RequiresPermissions("attendance:AutoClockList:edit")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "自动打卡清单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody AttendanceAutoClockList attendanceAutoClockList)
    {
        return toAjax(attendanceAutoClockListService.updateAttendanceAutoClockList(attendanceAutoClockList));
    }

    /**
     * 删除自动打卡清单
     */
    @ApiOperation(value = "删除自动打卡清单,ids,以逗号分割")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "自动打卡清单", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(@RequestParam(value = "ids", required = true)  String ids)
    {
        return toAjax(attendanceAutoClockListService.deleteAttendanceAutoClockListByIds(ids));
    }
}
