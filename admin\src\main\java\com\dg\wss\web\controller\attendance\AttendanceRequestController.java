package com.dg.wss.web.controller.attendance;

import cn.hutool.core.collection.CollUtil;
import com.dg.wss.attendance.domain.*;
import com.dg.wss.attendance.domain.vo.AttendanceRequestVo;
import com.dg.wss.attendance.domain.vo.RequestAwayVo;
import com.dg.wss.attendance.domain.vo.RequestRepeatVo;
import com.dg.wss.attendance.service.*;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.domain.model.LoginUser;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.common.exception.GlobalException;
import com.dg.wss.common.utils.DateUtils;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@Api(tags = "考勤审批申请")
@RestController
@RequestMapping("/attendance/attendanceRequest")
public class AttendanceRequestController extends BaseController {
    @Autowired
    private IAttendanceRequestService attendanceRequestService;
    @Autowired
    private IRequestRepeatService requestRepeatService;
    @Autowired
    private IRequestAwayService requestAwayService;
    @Autowired
    private IAwayColleagueService awayColleagueService;
    @Autowired
    private IAttendanceOTService iAttendanceOTService;
    @Autowired
    private IAttendanceService iAttendanceService;

    @ApiOperation(value = "添加审批申请")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime"})
    @DynamicResponseParameters(name = "attendanceRequest_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/add")
    @RepeatSubmit
    @Log(title = "考勤申请", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated @RequestBody AttendanceRequestVo attendanceRequest) {
        LoginUser user = getLoginUser();
        attendanceRequest.setCreateUser(user.getUserId());
        if (attendanceRequest.getCreateBy() == null) attendanceRequest.setCreateBy(attendanceRequest.getCreateUser());
        attendanceRequest.setCircleId(user.getCircleId());
        attendanceRequest.setMode(0);
        //synchronized (this) {
            return attendanceRequestService.addAttendanceRequest(attendanceRequest);
        //}
    }

    @ApiOperation(value = "批量添加审批申请")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime"})
    @DynamicResponseParameters(name = "attendanceRequests_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/adds")
    @RepeatSubmit
    @Log(title = "考勤申请", businessType = BusinessType.INSERT)
    public AjaxResult adds(@Validated @RequestBody AttendanceRequestVo attendanceRequest) {
        if (CollUtil.isEmpty(attendanceRequest.getUserIds())) return AjaxResult.error("未选中用户");
        LoginUser user = getLoginUser();
        attendanceRequest.setCreateUser(user.getUserId());
        attendanceRequest.setCircleId(user.getCircleId());
        //synchronized (this) {
            return attendanceRequestService.addAttendanceRequests(attendanceRequest);
        //}
    }

    @ApiOperation(value = "编辑审批申请")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime"})
    @DynamicResponseParameters(name = "attendanceRequest_edit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/edit")
    @RepeatSubmit
    @Log(title = "考勤申请", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated @RequestBody AttendanceRequestVo attendanceRequest) {
        return attendanceRequestService.editAttendanceRequest(attendanceRequest);
    }

    @ApiOperation(value = "分页关联查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "groupId", value = "考勤组ID", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "状态", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "repeat", value = "审批人ID", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "copy", value = "被抄送人ID", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "Date"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "Date")})
    @DynamicResponseParameters(name = "attendanceRequest_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = AttendanceRequestVo[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> map) {
        filterCircle(map);
        startPage();
        return getDataTable(attendanceRequestService.list(map));
    }

    @ApiOperation(value = "分页不关联查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "groupId", value = "考勤组ID", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "状态", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "repeat", value = "审批人ID", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "copy", value = "被抄送人ID", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "Date"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "Date")})
    @DynamicResponseParameters(name = "attendanceRequest_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = AttendanceRequest[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/selectList")
    public TableDataInfo selectList(@RequestParam Map<String, Object> map) {
        filterCircle(map);
        startPage();
        return getDataTable(attendanceRequestService.selectList(map));
    }

    @ApiOperation(value = "id查询")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AttendanceRequestVo.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("id") @RequestParam Long id) {
        return checkCircle(attendanceRequestService.getInfo(id));
    }

    @ApiOperation(value = "审批统计")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AttendanceRequestVo.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/countRequest")
    public AjaxResult countRequest(@ApiParam("考勤组id") @RequestParam(required = false) Long groupId, @ApiParam("用户ID") @RequestParam Long userId, @ApiParam("开始时间") @RequestParam Date start, @ApiParam("结束时间") @RequestParam Date end) {
        return AjaxResult.success(attendanceRequestService.countRequest(getCircleId(), groupId, userId, start, end));
    }

    @ApiOperation(value = "添加考勤审批")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime"})
    @DynamicResponseParameters(name = "requestAway_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/addRepeat")
    @RepeatSubmit
    @Log(title = "考勤审批", businessType = BusinessType.INSERT)
    public AjaxResult addRepeat(@Validated @RequestBody RequestRepeat requestRepeat) {
        requestRepeat.setCreateBy(getUserId());
        return toAjax(requestRepeatService.addRequestRepeat(requestRepeat));
    }

    @ApiOperation(value = "编辑考勤审批")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime"})
    @DynamicResponseParameters(name = "repeat_edit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/editRepeat")
    @RepeatSubmit
    @Log(title = "考勤审批", businessType = BusinessType.UPDATE)
    public AjaxResult editRepeat(@Validated @RequestBody RequestRepeat requestRepeat) {
        return requestRepeatService.editRequestRepeat(requestRepeat);//
    }

    @ApiOperation(value = "审批分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "Sting")})
    @DynamicResponseParameters(name = "repeatList_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = RequestRepeatVo[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/repeatList")
    public TableDataInfo repeatList(@RequestParam Map<String, Object> map) {
        filterCircle(map);
        startPage();
        return getDataTable(requestRepeatService.list(map));
    }

    @ApiOperation(value = "获取审批数量")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "Sting")})
    @DynamicResponseParameters(name = "selectCount", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = Integer.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/selectCount")
    public AjaxResult selectCount(@RequestParam Map<String, Object> map) {
        return AjaxResult.success(requestRepeatService.selectCount(map));
    }

    @ApiOperation(value = "id查询")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = RequestRepeat.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getRepeatInfo")
    public AjaxResult getRepeatInfo(@ApiParam("id") @RequestParam Long id) {
        return AjaxResult.success(requestRepeatService.getInfo(id));
    }

    @ApiOperation(value = "添加出差行程")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime"})
    @DynamicResponseParameters(name = "requestAway_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/addRequestAway")
    @RepeatSubmit
    @Log(title = "考勤出差", businessType = BusinessType.INSERT)
    public AjaxResult addRequestAway(@Validated @RequestBody RequestAwayVo requestAway) {
        return requestAwayService.addRequestAway(getUserId(), requestAway);
    }

    @ApiOperation(value = "编辑出差行程")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime"})
    @DynamicResponseParameters(name = "requestAway_edit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/editRequestAway")
    @RepeatSubmit
    @Log(title = "考勤出差", businessType = BusinessType.UPDATE)
    public AjaxResult editRepeat(@Validated @RequestBody RequestAway requestAway) {
        return requestAwayService.editRequestAway(getUserId(), requestAway);
    }

    @ApiOperation(value = "删除出差行程")
    @DynamicResponseParameters(name = "requestAway_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @DeleteMapping("/deleteRequestAway")
    @RepeatSubmit
    @Log(title = "考勤出差", businessType = BusinessType.DELETE)
    public AjaxResult deleteRequestAway(@Validated Long id) {
        return toAjax(requestAwayService.deleteRequestAway(id));
    }

    @ApiOperation(value = "出差分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "Sting")})
    @DynamicResponseParameters(name = "requestAway_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = RequestAway[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/requestAwayList")
    public TableDataInfo requestAwayList(@RequestParam Map<String, Object> map) {
        startPage();
        return getDataTable(requestAwayService.list(map));
    }

    @ApiOperation(value = "id查询")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = RequestAway.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getRequestAwayInfo")
    public AjaxResult getRequestAwayInfo(@ApiParam("id") @RequestParam Long id) {
        return AjaxResult.success(requestRepeatService.getInfo(id));
    }

    @ApiOperation(value = "添加同行人")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime"})
    @DynamicResponseParameters(name = "awayColleague_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/addAwayColleague")
    @RepeatSubmit
    @Log(title = "出差同行人", businessType = BusinessType.INSERT)
    public AjaxResult addAwayColleague(@Validated @RequestBody AwayColleague awayColleague) {
        return awayColleagueService.addAwayColleague(awayColleague);
    }

    @ApiOperation(value = "编辑同行人")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime"})
    @DynamicResponseParameters(name = "awayColleague_edit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/editAwayColleague")
    @RepeatSubmit
    @Log(title = "出差同行人", businessType = BusinessType.UPDATE)
    public AjaxResult editAwayColleague(@Validated @RequestBody AwayColleague requestAway) {
        return awayColleagueService.editAwayColleague(requestAway);
    }

    @ApiOperation(value = "删除同行人")
    @DynamicResponseParameters(name = "awayColleague_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @DeleteMapping("/deleteAwayColleague")
    @RepeatSubmit
    @Log(title = "出差同行人", businessType = BusinessType.DELETE)
    public AjaxResult deleteAwayColleague(@Validated Long id) {
        return toAjax(awayColleagueService.deleteAwayColleague(id));
    }

    @ApiOperation(value = "出差同行人分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "Sting")})
    @DynamicResponseParameters(name = "awayColleague_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = AwayColleague[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/awayColleagueList")
    public TableDataInfo awayColleagueList(@RequestParam Map<String, Object> map) {
        startPage();
        return getDataTable(awayColleagueService.list(map));
    }

    @ApiOperation(value = "id查询")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AwayColleague.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getAwayColleagueInfo")
    public AjaxResult getAwayColleagueInfo(@ApiParam("id") @RequestParam Long id) {
        return AjaxResult.success(awayColleagueService.getInfo(id));
    }

    @ApiOperation(value = "删除附件")
    @DynamicResponseParameters(name = "requestFile_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @DeleteMapping("/deleteFile")
    @RepeatSubmit
    @Log(title = "考勤附件", businessType = BusinessType.DELETE)
    public AjaxResult deleteFile(@RequestBody RequestFile file) {
        return toAjax(attendanceRequestService.deleteFile(file));
    }

    @ApiOperation(value = "计算请假的年假天数")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime"})
    @DynamicResponseParameters(name = "attendanceRequest_getHoliday", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/getHoliday")
    @RepeatSubmit
    public AjaxResult getHoliday(@Validated @RequestBody AttendanceRequestVo attendanceRequest) {
        attendanceRequest.setCreateBy(getUserId());
        attendanceRequest.setCircleId(getCircleId());
        synchronized (this) {
            return AjaxResult.success(attendanceRequestService.getHoliday(attendanceRequest));
        }
    }

    @ApiOperation(value = "获取加班日考勤类型，班前班后班中类型，检查加班时间范围是否和工作日排班交叉")
    @DynamicResponseParameters(name = "overtimeType_response", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", example = "attendanceDateType：7, 工作日加班；8，休息日加班；9，法定节假日加班；otShiftBeforeOrAfter：1，班前；2班后",
                    dataTypeClass = Integer.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/getOvertimeType")
    public AjaxResult getOvertimeType(
            @ApiParam("考勤组id") @RequestParam(required = true) Long groupId,
            @ApiParam("申请加班人员Id") @RequestParam(required = true) Long userId,
            @ApiParam("加班开始日期") @RequestParam(required = true) Date overTimeStartTime,
            @ApiParam("加班结束日期") @RequestParam(required = true) Date overTimeEndTime) {
        Map<String,String> results = new HashMap<String,String>();
        Date otTimeAttendanceDate = iAttendanceOTService.getOverTimeAttendanceDate(groupId,userId,overTimeStartTime);
        Integer attendanceDateType = iAttendanceService.getAttendanceDateTypeByUser(getCircleId(),userId,otTimeAttendanceDate);
        results.put("attendanceDateType",attendanceDateType.toString());
        if(attendanceDateType==7){
            Integer otShiftBeforeOrAfter = iAttendanceOTService.checkOTShiftBeforeOrAfter(groupId,userId,otTimeAttendanceDate
                    ,overTimeStartTime,overTimeEndTime);
            results.put("otShiftBeforeOrAfter",otShiftBeforeOrAfter.toString());
        }

        //校验加班申请是否重复
        Map<String, Object> map = new HashMap<>();
        map.put("circleId", this.getCircleId());
        map.put("userId", userId);
        map.put("type", "3");
        map.put("start", overTimeStartTime);
        map.put("end", overTimeEndTime);
        map.put("status", "0,1");
        List<AttendanceRequest> requests = attendanceRequestService.selectList(map);
        if (!requests.isEmpty()) {
            for(AttendanceRequest attendanceRequest:requests){
                if(DateUtils.inRange(overTimeStartTime,attendanceRequest.getStartTime(),attendanceRequest.getEndTime())){
                    throw new GlobalException("该时间段已有其他加班申请");
                }

                if(DateUtils.inRange(overTimeEndTime,attendanceRequest.getStartTime(),attendanceRequest.getEndTime())){
                    throw new GlobalException("该时间段已有其他加班申请");
                }
            }
        }
        return AjaxResult.success(results);
    }
}
