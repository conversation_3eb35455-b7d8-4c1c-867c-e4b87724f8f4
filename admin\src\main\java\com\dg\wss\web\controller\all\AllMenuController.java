package com.dg.wss.web.controller.all;

import com.dg.wss.all.domain.AllMenu;
import com.dg.wss.all.service.IAllMenuService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Date;
import java.util.List;
import java.util.Map;
@Api(tags = "统一菜单")
@RestController
@RequestMapping("/all/allMenu")
public class AllMenuController extends BaseController {
    @Autowired
    private IAllMenuService allMenuService;
    
    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
                 @ApiImplicitParam(paramType = "query", name = "id", value = "id", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "name", value = "名称", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "path", value = "路由地址", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "perms", value = "权限字符", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "pid", value = "父级", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "no", value = "顺序", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "type", value = "类型，aps", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "status", value = "状态 -1隐藏 0默认", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createBy", value = "创建人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createTime", value = "创建时间", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateBy", value = "更新人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateTime", value = "更新时间", dataType = "Sting"),
    })
    @DynamicResponseParameters(name = "allMenu_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = AllMenu[].class)
    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        List<AllMenu> list = allMenuService.pageByMap(map);
        return getDataTable(list);
    }

    @GetMapping("/treeList")
    public TableDataInfo treeList(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        List<AllMenu> list = allMenuService.treeList(map);
        return getDataTable(list);
    }
    
    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "allMenu_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "统一菜单", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody AllMenu allMenu) {
        allMenu.setCreateBy(getUserId());
        allMenu.setCreateTime(new Date());
        return AjaxResult.success(allMenuService.insert(allMenu));
    }
    
    @ApiOperation(value = "添加多个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "collateralRate_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @RepeatSubmit
    @PostMapping("/addlist")
    public AjaxResult addlist(@RequestBody List<AllMenu> allMenus) {
        return toAjax(allMenuService.batchInsert(allMenus, getUserId()));
    }
    
    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "allMenu_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "统一菜单", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody AllMenu allMenu) {
        allMenu.setUpdateBy(getUserId());
        allMenu.setUpdateTime(new Date());
        return toAjax(allMenuService.update(allMenu));
    }
    
    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AllMenu.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(allMenuService.selectById(id));
    }
    
    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "allMenu_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "统一菜单", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(allMenuService.delete(id));
    }
}