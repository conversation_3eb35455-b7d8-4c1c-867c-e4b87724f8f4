package com.dg.wss.web.controller.all;

import com.dg.wss.all.domain.AllColor;
import com.dg.wss.all.service.IAllColorService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 系统颜色 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-28
 */
@RestController
@RequestMapping("/all/allColor")
public class AllColorController extends BaseController {
    @Autowired
    private IAllColorService allColorService;


    /**
     * 查询系统颜色列表
     */
    @ApiOperation(value = "查询系统颜色列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo list(@ModelAttribute AllColor allColor) {
        fillTime(allColor);
        startPage();
        List<AllColor> list = allColorService.listAllColor(allColor);
        return getDataTable(list);
    }

    /**
     * 查询系统颜色详情
     */
    @ApiOperation(value = "查询系统颜色详情")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult list(@RequestParam Long id) {
        return AjaxResult.success(allColorService.getById(id));
    }

    /**
    * 新增系统颜色信息
    */
    @ApiOperation(value = "新增系统颜色信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统颜色", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@RequestBody AllColor allColor){
        allColor.setCreateBy(getUserId());
        allColor.setCircleId(getCircleId());
        allColor.setCreateTime(new Date());
        return AjaxResult.success(allColorService.save(allColor));
    }

    /**
    * 批量保存系统颜色
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统颜色", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAdd")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchAdd(@RequestBody List<AllColor> allColors) {
        for (AllColor allColor : allColors) {
            allColor.setCircleId(getCircleId());
            allColor.setCreateBy(getUserId());
            allColor.setCreateTime(new Date());
        }
        return AjaxResult.success(allColorService.saveBatch(allColors));
    }

    /**
     * 修改系统颜色信息
     */
    @ApiOperation(value = "修改系统颜色信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统颜色", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@RequestBody AllColor allColor){
        allColor.setCircleId(getCircleId());
        return AjaxResult.success(allColorService.updateById(allColor));
    }

    /**
    * 批量修改系统颜色
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统颜色", businessType = BusinessType.UPDATE)
    @PutMapping("/batchEdit")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchEdit(@RequestBody List<AllColor> allColors) {
        for (AllColor allColor : allColors) {
            allColor.setCircleId(getCircleId());
        }
        return AjaxResult.success(allColorService.updateBatchById(allColors));
    }

    /**
     * 删除系统颜色信息
     */
    @ApiOperation(value = "删除系统颜色信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统颜色", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable String id){
        return AjaxResult.success(allColorService.removeById(id));
    }
}
