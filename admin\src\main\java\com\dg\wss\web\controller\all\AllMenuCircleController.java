package com.dg.wss.web.controller.all;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;

import com.dg.wss.all.domain.AllMenuCircle;
import com.dg.wss.all.service.IAllMenuCircleService;

import java.util.Date;
import java.util.List;

import org.springframework.web.bind.annotation.RestController;
import com.dg.wss.commonservice.BaseController;

/**
 * <p>
 * 每个圈子的菜单配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@RestController
@RequestMapping("/all/allMenuCircle")
public class AllMenuCircleController extends BaseController {
    @Autowired
    private IAllMenuCircleService allMenuCircleService;


    /**
     * 查询每个圈子的菜单配置列表
     */
    @ApiOperation(value = "查询每个圈子的菜单配置列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo list(@ModelAttribute AllMenuCircle allMenuCircle) {
        fillTime(allMenuCircle);
//        startPage();
        allMenuCircle.setCircleId(getCircleId());
        List<AllMenuCircle> list = allMenuCircleService.listByType(allMenuCircle);
        return getDataTable(list);
    }

    /**
     * 查询每个圈子的菜单配置详情
     */
    @ApiOperation(value = "查询每个圈子的菜单配置详情")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult list(@RequestParam Long id) {
        return AjaxResult.success(allMenuCircleService.getById(id));
    }

    /**
    * 新增每个圈子的菜单配置信息
    */
    @ApiOperation(value = "新增每个圈子的菜单配置信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "每个圈子的菜单配置", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@RequestBody AllMenuCircle allMenuCircle){
        allMenuCircle.setCreateBy(getUserId());
        allMenuCircle.setCircleId(getCircleId());
        allMenuCircle.setCreateTime(new Date());
        return AjaxResult.success(allMenuCircleService.save(allMenuCircle));
    }

    /**
    * 批量保存每个圈子的菜单配置
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "每个圈子的菜单配置", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAdd")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchAdd(@RequestBody List<AllMenuCircle> allMenuCircles) {
        for (AllMenuCircle allMenuCircle : allMenuCircles) {
            allMenuCircle.setCircleId(getCircleId());
            allMenuCircle.setCreateBy(getUserId());
            allMenuCircle.setCreateTime(new Date());
        }
        return AjaxResult.success(allMenuCircleService.saveBatch(allMenuCircles));
    }

    /**
     * 修改每个圈子的菜单配置信息
     */
    @ApiOperation(value = "修改每个圈子的菜单配置信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "每个圈子的菜单配置", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@RequestBody AllMenuCircle allMenuCircle){
        allMenuCircle.setUpdateBy(getUserId());
        allMenuCircle.setCircleId(getCircleId());
        allMenuCircle.setUpdateTime(new Date());
        return AjaxResult.success(allMenuCircleService.updateById(allMenuCircle));
    }

    /**
    * 批量修改每个圈子的菜单配置
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "每个圈子的菜单配置", businessType = BusinessType.UPDATE)
    @PutMapping("/batchEdit")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchEdit(@RequestBody List<AllMenuCircle> allMenuCircles) {
        for (AllMenuCircle allMenuCircle : allMenuCircles) {
            allMenuCircle.setCircleId(getCircleId());
            allMenuCircle.setUpdateBy(getUserId());
            allMenuCircle.setUpdateTime(new Date());
        }
        return AjaxResult.success(allMenuCircleService.updateBatchById(allMenuCircles));
    }

    /**
     * 删除每个圈子的菜单配置信息
     */
    @ApiOperation(value = "删除每个圈子的菜单配置信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "每个圈子的菜单配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable String id){
        return AjaxResult.success(allMenuCircleService.removeById(id));
    }
}
