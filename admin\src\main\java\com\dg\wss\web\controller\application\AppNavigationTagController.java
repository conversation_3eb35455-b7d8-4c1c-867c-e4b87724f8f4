package com.dg.wss.web.controller.application;

import com.dg.wss.application.domain.AppPageComponent;
import com.dg.wss.application.vo.AppNavigationTagQueryVo;
import com.dg.wss.application.vo.AppPageComponentQueryVo;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.validate.CreateGroup;
import com.dg.wss.common.core.validate.UpdateGroup;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.application.service.IAppNavigationTagService;
import com.dg.wss.application.vo.AppNavigationTagVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.groups.Default;

@Api(tags = "导航标签管理")
@RestController
@RequestMapping("/navigation/tag")
public class AppNavigationTagController extends BaseController {

    @Autowired
    private IAppNavigationTagService navigationTagService;

    @ApiOperation(value = "创建导航标签")
    @ApiOperationSupport(ignoreParameters = {"id", "deleteFlag", "createTime", "updateTime", "createBy", "updateBy"})
    @PostMapping("/add")
    @RepeatSubmit
    @Log(title = "导航标签新增", businessType = BusinessType.INSERT)
    public AjaxResult createNavigationTag(@RequestBody @Validated({Default.class, CreateGroup.class}) AppNavigationTagVo vo) {
        navigationTagService.createAppPageComponent( vo);
        return AjaxResult.success("导航标签创建成功");
    }

    @ApiOperation(value = "修改导航标签")
    @ApiOperationSupport(ignoreParameters = {"deleteFlag", "createTime", "updateTime", "createBy", "updateBy"})
    @PutMapping("/update")
    @RepeatSubmit
    @Log(title = "导航标签更新", businessType = BusinessType.UPDATE)
    public AjaxResult updateNavigationTag(@RequestBody @Validated({Default.class, UpdateGroup.class}) AppNavigationTagVo vo) {
        navigationTagService.updateAppPageComponent(vo);
        return AjaxResult.success("导航标签更新成功");
    }

    @ApiOperation(value = "删除导航标签")
    @DeleteMapping("/remove/{id}")
    @RepeatSubmit
    @Log(title = "导航标签删除", businessType = BusinessType.DELETE)
    public AjaxResult deleteNavigationTag(@PathVariable Long id) {
        navigationTagService.deleteAppPageComponentById(id);
        return AjaxResult.success("导航标签删除成功");
    }

    @DeleteMapping("removeByKey/{key}")
    @ApiOperation("根据组件key删除导航标签")
    @Log(title = "根据组件key删除导航标签", businessType = BusinessType.DELETE)
    public AjaxResult remove(@PathVariable String key) {
        navigationTagService.deleteAppPageComponentByKey(key);
        return AjaxResult.success("按钮删除导航标签");
    }

    @GetMapping("/list")
    @ApiOperation("分页查询导航标签列表")
    public TableDataInfo listNavigationTags(AppNavigationTagQueryVo queryVo) {
        startPage();
        return getDataTable(navigationTagService.listAppPageComponents(queryVo));
    }

    @GetMapping("/getInfo")
    @ApiOperation("获取导航标签详情")
    public AjaxResult getNavigationTagDetails(@ApiParam("标签ID") @RequestParam Long id) {
        AppPageComponent tag = navigationTagService.getAppPageComponentById(id);
        return AjaxResult.success(tag);
    }
}
