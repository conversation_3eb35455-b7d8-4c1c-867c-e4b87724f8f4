package com.dg.wss.web.controller.aps;

import com.dg.wss.aps.domain.ApsFile;
import com.dg.wss.aps.service.IApsFileService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Api(tags = "aps附件")
@RestController
@RequestMapping("/aps/apsFile")
public class ApsFileController extends BaseController {
    @Autowired
    private IApsFileService apsFileService;

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<ApsFile> list = apsFileService.pageByMap(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps附件", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ApsFile apsFile) {
        apsFile.setCreateBy(getUserId());
        apsFile.setCreateTime(new Date());
        return toAjax(apsFileService.insert(apsFile));
    }

    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps附件", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody ApsFile apsFile) {
        apsFile.setUpdateBy(getUserId());
        apsFile.setUpdateTime(new Date());
        return toAjax(apsFileService.update(apsFile));
    }

    @ApiOperation(value = "查询by id")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(apsFileService.selectById(id));
    }

    @ApiOperation(value = "删除")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps附件", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(apsFileService.delete(id));
    }
}