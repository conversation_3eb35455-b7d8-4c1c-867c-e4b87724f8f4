package com.dg.wss.web.controller.application;

import com.dg.wss.application.domain.AppMenu;
import com.dg.wss.application.vo.AppMenuQueryVo;
import com.dg.wss.application.service.IAppMenuService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.core.validate.CreateGroup;
import com.dg.wss.common.core.validate.UpdateGroup;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.groups.Default;

@RestController
@RequestMapping("/application/menu")
@Api("菜单管理")
public class AppMenuController extends BaseController {

    @Autowired
    private IAppMenuService appMenuService;

    @GetMapping("/list")
    @ApiOperation("分页查询")
    public TableDataInfo list(AppMenuQueryVo queryVo) {
        startPage();
        return getDataTable(appMenuService.listAppPageComponents(queryVo));
    }

    @PostMapping
    @ApiOperation("新增菜单")
    @Log(title = "新增菜单", businessType = BusinessType.INSERT)
    @RepeatSubmit
    public AjaxResult add(@RequestBody @Validated({Default.class, CreateGroup.class}) AppMenu menu) {
        appMenuService.createAppPageComponent(menu);
        return AjaxResult.success("创建菜单成功", menu);
    }

    @PutMapping
    @ApiOperation("修改菜单")
    @Log(title = "修改菜单", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    public AjaxResult edit(@RequestBody @Validated({Default.class, UpdateGroup.class}) AppMenu menu) {
        appMenuService.updateAppPageComponent( menu);
        return AjaxResult.success("修改菜单成功", menu);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除菜单")
    @Log(title = "删除菜单", businessType = BusinessType.DELETE)
    public AjaxResult remove(@PathVariable Long id) {
        appMenuService.deleteAppPageComponentById( id);
        return AjaxResult.success("菜单删除成功");
    }


    @DeleteMapping("removeByKey/{key}")
    @ApiOperation("根据组件key删除菜单")
    @Log(title = "根据组件key删除菜单", businessType = BusinessType.DELETE)
    public AjaxResult remove(@PathVariable String key) {
        appMenuService.deleteAppPageComponentByKey(key);
        return AjaxResult.success("删除菜单成功");
    }
}
