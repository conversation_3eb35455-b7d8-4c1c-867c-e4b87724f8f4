package com.dg.wss.web.controller.aps;

import com.dg.wss.aps.domain.ApsLotMo;
import com.dg.wss.aps.domain.ApsReport;
import com.dg.wss.aps.domain.ApsTask;
import com.dg.wss.aps.domain.ApsTaskScanCard;
import com.dg.wss.aps.domain.vo.ApsTaskVo;
import com.dg.wss.aps.service.IApsTaskScanCardService;
import com.dg.wss.aps.service.IApsTaskService;
import com.dg.wss.ccs.service.ICcsService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.domain.UnencryptedResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.common.utils.SecurityUtils;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.hr.domain.vo.HOrganizationVo;
import com.dg.wss.scm.domain.wms.vo.ScmStockParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
@Api(tags = "aps task")
@RestController
@RequestMapping("/aps/apsTask")
public class ApsTaskController extends BaseController {
    @Autowired
    private IApsTaskService apsTaskService;
    @Autowired
    private IApsTaskScanCardService apsTaskScanCardService;
    @Autowired
    private ICcsService ccsService;

    /**
     * 一次录入多行批次，新增保存aps_task 扫卡明细记录
     */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps_task扫卡明细记录", businessType = BusinessType.INSERT)
    @PostMapping("/scanCard/batchAdd")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult batchAdd(@RequestBody List<ApsTaskScanCard> apsTaskScanCards)
    {
        return toAjax(apsTaskScanCardService.batchInsertApsTaskScanCard(apsTaskScanCards));
    }
    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
                 @ApiImplicitParam(paramType = "query", name = "id", value = "主键ID", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "lotId", value = "lot id", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "moId", value = "mo id", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "processId", value = "工艺id", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "num", value = "数量", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createBy", value = "创建人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createTime", value = "创建时间", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateBy", value = "更新人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateTime", value = "更新时间", dataType = "Sting"),
    })
    @DynamicResponseParameters(name = "apsTask_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = ApsTask[].class)
    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        List<ApsTask> list = apsTaskService.pageByMap(map);
        return getDataTable(list);
    }
    @ApiOperation(value = "分页查询,附带产品信息")
    @GetMapping("/listVo")///aps/apsTask/listVo
    public TableDataInfo listVo(@ApiIgnore @RequestParam Map<String, Object> map) {
        map.put("circleId",getCircleId());
        fillTimeForMap(map);
        List<ApsTaskVo> list = apsTaskService.pageVoByMap(map);//汇总信息过滤条件要注意一致
        return getDataTable(list);
    }

    @ApiOperation(value = "各工序计划达成率")
    @PostMapping("/planAchievement")///aps/apsTask/planAchievement
    public TableDataInfo planAchievement(@Validated @RequestBody ApsTask apsTask) {
        if(apsTask.getCircleId()==null)apsTask.setCircleId(getCircleId());
        fillTime(apsTask);
        List<HOrganizationVo> list = apsTaskService.planAchievement(apsTask);
        return getDataTable(list);
    }

    @ApiOperation(value = "各科计划达成率")
    @PostMapping("/planAchievementForOrg")///aps/apsTask/planAchievement
    public TableDataInfo planAchievementForOrg(@Validated @RequestBody ApsTask apsTask) {
        if(apsTask.getCircleId()==null)apsTask.setCircleId(getCircleId());
        fillTime(apsTask);
        List<ApsTaskVo> list = apsTaskService.planAchievementForOrg(apsTask);
        return getDataTable(list);
    }

    @ApiOperation(value = "分页查询,附带产品信息，汇总")
    @GetMapping("/listVoTotal")
    public TableDataInfo listVoTotal(@ApiIgnore @RequestParam Map<String, Object> map) {
        map.put("circleId",getCircleId());
        fillTimeForMap(map);
        List<ApsTaskVo> list = apsTaskService.pageVoByMapTotal(map);//明细信息过滤条件要注意一致
        return getDataTable(list);
    }
    
    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsTask_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps task", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ApsTask apsTask) {
        apsTask.setCreateBy(getUserId());
        apsTask.setCreateTime(new Date());
        return toAjax(apsTaskService.insert(apsTask));
    }

    @ApiOperation(value = "排子件")
    @Log(title = "aps 排子件", businessType = BusinessType.INSERT)
    @PostMapping("/planSubProduct")
    public AjaxResult planSubProduct(@Validated @RequestBody ApsLotMo apsLotMo) {
        apsLotMo.setCreateBy(getUserId());
        apsLotMo.setCreateTime(new Date());
        apsTaskService.planSubProduct(apsLotMo.getLotId(), getCircleId(), apsLotMo.getIdList());
        return AjaxResult.success();
    }


    @ApiOperation(value = "排自制单，生成工艺任务，分配到机器")
    @Log(title = "排程 aps planLot", businessType = BusinessType.INSERT)
    @PostMapping("/planSingleWorkOrder")
    public AjaxResult planSingleWorkOrder(@Validated @RequestBody ApsTask apsTask) {
        synchronized (this) {
            apsTaskService.createTaskAndPlanFromMo(apsTask.getMoId(), getCircleId(), null);
            return AjaxResult.success();
        }
    }

    @ApiOperation(value = "按工艺生成工艺任务")
    @Log(title = "排程 aps planLot", businessType = BusinessType.INSERT)
    @PostMapping("/createTaskByProcess")
    public AjaxResult createTaskByProcess( @RequestBody ApsTask apsTask) {
        synchronized (this) {
            apsTaskService.createTaskByProcess(apsTask);
            return AjaxResult.success();
        }
    }

    @ApiOperation(value = "撤销单独排产")
    @Log(title = "撤销单独排产", businessType = BusinessType.UPDATE)
    @PostMapping("/revokeSingleWorkOrder")
    public AjaxResult revokeSingleWorkOrder(@RequestParam Long moId) {
        synchronized (this) {
            apsTaskService.revokeSingleWorkOrder(moId);
            return AjaxResult.success();
        }
    }
    
    //@ApiOperation(value = "添加多个")
    //@ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    //@DynamicResponseParameters(name = "collateralRate_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            //@DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    //@PreAuthorize("@ss.hasRole('basics')")
    //@RepeatSubmit
    //@PostMapping("/addlist")
    //public AjaxResult addlist(@RequestBody List<ApsTask> apsTasks) {
        //return toAjax(apsTaskService.batchInsert(apsTasks, getUserId()));
    //}
    
    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsTask_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps task", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody ApsTask apsTask) {
        apsTask.setUpdateBy(getUserId());
        apsTask.setUpdateTime(new Date());
        return toAjax(apsTaskService.update(apsTask));
    }

    @ApiOperation(value = "修改任务开始时间和结束时间，并同步修改所有 taskResource")
    @RepeatSubmit
    @Log(title = "修改任务开始时间和结束时间", businessType = BusinessType.UPDATE)
    @PutMapping("/editStartEndTime")
    public AjaxResult editStartEndTime(@RequestBody ApsTask apsTask) {
        apsTask.setUpdateBy(getUserId());
        apsTask.setUpdateTime(new Date());
        return toAjax(apsTaskService.editStartEndTime(apsTask));
    }

    @ApiOperation(value = "修改任务状态，并计时")
    @Log(title = "修改任务状态，并计时", businessType = BusinessType.UPDATE)
    @PutMapping("/timing")
    public AjaxResult timing(@RequestBody ApsTask apsTask) {
//        apsTask.setUpdateBy(getUserId());
//        apsTask.setUpdateTime(new Date());
        apsTask.setCircleId(getCircleId());
        return apsTaskService.timing(apsTask);
    }
    
    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ApsTask.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(apsTaskService.selectById(id));
    }

    @GetMapping("/getInfoVoForScanCard")
    public AjaxResult getInfoVoForScanCard(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(apsTaskService.getInfoVoForScanCard(id));
    }
    
    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "apsTask_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps task", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(apsTaskService.delete(id));
    }


    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/scanCard/list")
    @ResponseBody
    public TableDataInfo list(@RequestBody ApsTaskScanCard apsTaskScanCard)
    {
        startPage();
        List<ApsTaskScanCard> list = apsTaskScanCardService.selectApsTaskScanCardList(apsTaskScanCard);
        return getDataTable(list);
    }

    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ApsTask.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/scanCard/getInfo")
    public AjaxResult selectScanCardById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(apsTaskScanCardService.getById(id));
    }

    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/getOtherList")
    public AjaxResult syncOtherScanCard(@RequestBody ApsTaskScanCard apsTaskScanCard) {
        return AjaxResult.success(apsTaskScanCardService.syncOtherScanCard(apsTaskScanCard));
    }

    /**
     * 新增保存aps_task 扫卡明细记录
     */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps_task扫卡明细记录", businessType = BusinessType.INSERT)
    @PostMapping("/scanCard/add")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult addSave(@RequestBody ApsTaskScanCard apsTaskScanCard)
    {
        apsTaskScanCard.setOrganizationId(SecurityUtils.getLoginUser().getOrganizationId());
        return toAjax(apsTaskScanCardService.insertApsTaskScanCard(apsTaskScanCard));
    }

    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "设备上报扫卡记录", businessType = BusinessType.INSERT)
    @PostMapping("/scanCard/deviceUpload")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult deviceUpload(@RequestBody ApsTaskScanCard apsTaskScanCard)
    {
        return toAjax(apsTaskScanCardService.deviceUpload(apsTaskScanCard.getDeviceUploadText()));
    }


    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps_task扫卡明细记录", businessType = BusinessType.INSERT)
    @PostMapping("/scanCard/scanCardCompleteForLots")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult scanCardCompleteForLots(@RequestBody ApsTaskScanCard apsTaskScanCard)
    {
        apsTaskScanCard.setOrganizationId(SecurityUtils.getLoginUser().getOrganizationId());
        return toAjax(apsTaskScanCardService.scanCardCompleteForLots(apsTaskScanCard.getLotIdList()));
    }

    /**
     * 主动完成扫卡任务
     */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps_task扫卡明细记录", businessType = BusinessType.INSERT)
    @PostMapping("/scanCard/scanCardComplete")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult scanCardComplete(@RequestBody ApsTaskScanCard apsTaskScanCard) {
        return toAjax(apsTaskScanCardService.scanCardComplete(apsTaskScanCard, getLoginUser().getUser(), getCircleId()));
    }

    /**
     * 投料前的查询
     */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "投料", businessType = BusinessType.INSERT)
    @PostMapping("/issueList")//apsTask/issueList
    @ResponseBody
    public TableDataInfo issueList(@RequestBody ScmStockParam param)
    {
        param.setCircleId(getCircleId());
        return getDataTable(apsTaskService.issueList(param));
    }

    /**
     * 投料
     */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "投料", businessType = BusinessType.INSERT)
    @PostMapping("/issue")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult issue(@RequestBody ScmStockParam param)
    {
        return AjaxResult.success(apsTaskService.issue(param));
    }

    @ApiOperation(value = "分部门查询task 进度")
    @GetMapping("/groupByOrganization")
    public TableDataInfo groupByOrganization(@ApiIgnore @RequestParam Map<String, Object> map) {
        map.put("circleId",getCircleId());
        fillTimeForMap(map);
        return getDataTable(apsTaskService.groupByOrganization(map));
    }

    @ApiOperation(value = "分部门递归查询task 进度")
    @GetMapping("/groupByOrganizationCte")
    public TableDataInfo groupByOrganizationCte(@ApiIgnore @RequestParam Map<String, Object> map) {
        map.put("circleId",getCircleId());
        fillTimeForMap(map);
        return getDataTable(apsTaskService.groupByOrganizationCte(map));
    }

    @ApiOperation(value = "按人分组 查询task 进度")
    @GetMapping("/groupByUser")
    public TableDataInfo groupByUser(@ApiIgnore @RequestParam Map<String, Object> map) {
        map.put("circleId",getCircleId());
        return getDataTable(apsTaskService.groupByUser(map));
    }

    @ApiOperation(value = "扫二维码查询lot里面我的任务")
    @GetMapping("/listTaskByLotAndUser")
    public TableDataInfo listTaskByLotAndUser(@ApiIgnore @RequestParam Map<String, Object> map) {
        map.put("circleId",getCircleId());
        Long userId =map.containsKey("userId")? Long.parseLong(map.get("userId").toString()) : getUserId();
//        if(userId==null) userId=getUserId();
        return getDataTable(apsTaskService.listTaskByLotAndUser(map, userId, getUserId()));
    }

    @ApiOperation(value = "根据任务id查询需要确认数量的产品列表")
    @GetMapping("/ListForCheckNum")
    public AjaxResult ListForCheckNum(@ApiIgnore @RequestParam Long taskId) {
        return AjaxResult.success(apsTaskService.ListForCheckNum(taskId));
    }

    @ApiOperation(value = "扫卡确认")
    @Log(title = "扫卡确认", businessType = BusinessType.INSERT)
    @PostMapping("/scanCardConfirmCounts")
    public AjaxResult scanCardConfirmCounts(@ApiIgnore @RequestBody ApsTaskVo apsTaskVo) {
        return AjaxResult.success(apsTaskService.scanCardConfirmCounts(apsTaskVo));
    }


    @ApiOperation(value = "生产计划查询", notes = "原有生产计划接口，支持CCS模式")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/makePlan")
    public UnencryptedResult makePlan(@ApiIgnore @RequestParam Map<String, Object> map) {
        // 检测是否为CCS模式
        boolean isCcsMode = "1".equals(map.get("ccsMode"));
        
        if (isCcsMode) {
            // CCS模式：使用CCS Service处理
            return handleCcsMakePlan(map);
        } else {
            // 原有模式：保持原逻辑不变
            startPage();
            map.put("circleId", getCircleId());
            return UnencryptedResult.success(apsTaskService.makePlan(map));
        }
    }


    //拿车间的工艺分布
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/makePlanGetProcess")
    public TableDataInfo makePlanGetProcess(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        return getDataTable(apsTaskService.makePlanGetProcess(map));
    }

    @ApiOperation(value = "根据自制单查询工艺，附带查询任务task, 参数moId")
    @GetMapping("/listMIByMo")
    public TableDataInfo listMIByMo(@ApiIgnore @RequestParam Map<String,Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        return getDataTable(apsTaskService.listMIByMo(map));
    }

    //工艺出版数量表 报表1227
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/listOfCard")
    public UnencryptedResult listOfCard(@RequestBody ApsReport param){
        startPage();
        fillTime(param);
        return UnencryptedResult.success(apsTaskService.listOfCard(param));
    }


    //临时出报表，用于检验大屏
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/tempTotal")
    public AjaxResult tempTotal(@RequestBody ApsReport param){
        startPage();
        fillTime(param);
       // if(param.getCircleId()==null) param.setCircleId(getCircleId());
        return AjaxResult.success(apsTaskService.tempTotal(param));
    }

    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps_task扫卡明细记录", businessType = BusinessType.INSERT)
    @PostMapping("/scanCard/undo")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult undo(@RequestBody ApsTaskScanCard apsTaskScanCard)
    {
        return toAjax(apsTaskScanCardService.undo(apsTaskScanCard));
    }
    
    /**
     * CCS模式生产计划处理
     */
    private UnencryptedResult handleCcsMakePlan(Map<String, Object> map) {
        Map<String, Object> aggregatedResult = ccsService.processCcsMultiCircleData(
            getUserId(),
            map,
            new HashMap<String, Object>(),
            // processor: 处理单个圈子的生产计划数据
            (circleId, companyIds, circleMap, userId) -> {
                return apsTaskService.makePlan(circleMap);
            },
            // aggregator: 聚合生产计划结果
            (result, circleResult) -> {
                mergeWipPlanData(result, circleResult, null);
            },
            // empty supplier: 空结果构造器
            () -> new HashMap<String, Object>()
        );
        
        return UnencryptedResult.success(aggregatedResult);
    }
    
    /**
     * 合并多个圈子的WIP生产计划数据
     * 数据格式：{ "total": "122", "表体": [...], "表头": [...] }
     */
    private void mergeWipPlanData(Map<String, Object> aggregated, Map<String, Object> circleResult, Long circleId) {
        if (circleResult == null || circleResult.isEmpty()) {
            return;
        }
        
        // 如果是第一次添加数据，初始化结构
        if (aggregated.isEmpty()) {
            // 复制基础结构（表头等）
            aggregated.putAll(circleResult);
            return;
        }
        
        // 合并 total 字段（字符串类型的数值）
        if (circleResult.containsKey("total")) {
            String newTotal = (String) circleResult.get("total");
            String existingTotal = (String) aggregated.get("total");
            
            try {
                int newTotalInt = Integer.parseInt(newTotal);
                int existingTotalInt = Integer.parseInt(existingTotal);
                aggregated.put("total", String.valueOf(existingTotalInt + newTotalInt));
            } catch (NumberFormatException e) {
                // 如果解析失败，保持原有逻辑
                aggregated.put("total", existingTotal);
            }
        }
        
        // 合并 表体 数据（List类型）
        if (circleResult.containsKey("表体")) {
            @SuppressWarnings("unchecked")
            List<Object> existingList = (List<Object>) aggregated.get("表体");
            @SuppressWarnings("unchecked") 
            List<Object> newList = (List<Object>) circleResult.get("表体");
            
            if (existingList != null && newList != null) {
                existingList.addAll(newList);
            }
        }
        
        // 表头字段不需要合并，保持第一个圈子的表头结构即可
        // 其他字段按原有逻辑处理
        for (Map.Entry<String, Object> entry : circleResult.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            // 跳过已处理的特殊字段
            if ("total".equals(key) || "表体".equals(key) || "表头".equals(key)) {
                continue;
            }
            
            if (value instanceof List) {
                // 合并其他列表数据
                @SuppressWarnings("unchecked")
                List<Object> existingList = (List<Object>) aggregated.computeIfAbsent(key, k -> new ArrayList<>());
                @SuppressWarnings("unchecked") 
                List<Object> newList = (List<Object>) value;
                existingList.addAll(newList);
            } else if (value instanceof Number) {
                // 合并数值数据
                if (aggregated.containsKey(key)) {
                    Object existing = aggregated.get(key);
                    if (existing instanceof Number) {
                        double sum = ((Number) existing).doubleValue() + ((Number) value).doubleValue();
                        // 保持原有数据类型
                        if (existing instanceof Integer && value instanceof Integer) {
                            aggregated.put(key, (int) sum);
                        } else if (existing instanceof Long && value instanceof Long) {
                            aggregated.put(key, (long) sum);
                        } else {
                            aggregated.put(key, sum);
                        }
                    }
                } else {
                    aggregated.put(key, value);
                }
            } else {
                // 其他类型数据，如果不存在则添加（避免覆盖）
                aggregated.putIfAbsent(key, value);
            }
        }
    }

}