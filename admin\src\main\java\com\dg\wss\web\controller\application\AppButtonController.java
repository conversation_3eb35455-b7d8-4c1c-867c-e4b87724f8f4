package com.dg.wss.web.controller.application;

import com.dg.wss.application.domain.AppButton;
import com.dg.wss.application.vo.AppButtonQueryVo;
import com.dg.wss.application.vo.AppPageComponentQueryVo;
import com.dg.wss.application.service.IAppButtonService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.core.validate.CreateGroup;
import com.dg.wss.common.core.validate.UpdateGroup;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.groups.Default;

@RestController
@RequestMapping("/application/button")
@Api("按钮管理")
public class AppButtonController extends BaseController {

    @Autowired
    private IAppButtonService appButtonService;

    @GetMapping("/list")
    @ApiOperation("分页查询")
    public TableDataInfo list(AppButtonQueryVo queryVo) {
        startPage();
        return getDataTable(appButtonService.listAppPageComponents( queryVo));
    }

    @PostMapping
    @ApiOperation("新增按钮")
    @Log(title = "新增按钮", businessType = BusinessType.INSERT)
    @RepeatSubmit
    public AjaxResult add(@RequestBody @Validated({Default.class, CreateGroup.class}) AppButton button) {
        appButtonService.createAppPageComponent(button);
        return AjaxResult.success("创建按钮成功", button);
    }

    @PutMapping
    @ApiOperation("修改按钮")
    @Log(title = "修改按钮", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    public AjaxResult edit(@RequestBody @Validated({Default.class, UpdateGroup.class}) AppButton button) {
        appButtonService.updateAppPageComponent( button);
        return AjaxResult.success("修改按钮成功", button);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除按钮")
    @Log(title = "删除按钮", businessType = BusinessType.DELETE)
    public AjaxResult removeById(@PathVariable Long id) {
        appButtonService.deleteAppPageComponentById( id);
        return AjaxResult.success("按钮删除成功");
    }

    @DeleteMapping("removeByKey/{key}")
    @ApiOperation("根据组件key删除按钮")
    @Log(title = "根据组件key删除按钮", businessType = BusinessType.DELETE)
    public AjaxResult removeByKey(@PathVariable String key) {
        appButtonService.deleteAppPageComponentByKey(key);
        return AjaxResult.success("按钮删除成功");
    }

    @DeleteMapping("removeByKeyIncludeChildren/{key}")
    @ApiOperation("根据组件key删除按钮")
    @Log(title = "根据组件key删除按钮", businessType = BusinessType.DELETE)
    public AjaxResult removeByKeyIncludeChildren(@PathVariable String key) {
        appButtonService.deleteAppPageComponentByKeyIncludeChildren(key);
        return AjaxResult.success("按钮删除成功");
    }
}
