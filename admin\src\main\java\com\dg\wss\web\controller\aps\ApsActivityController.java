package com.dg.wss.web.controller.aps;

import com.dg.wss.aps.domain.ApsActivity;
import com.dg.wss.aps.domain.ApsActivityVo;
import com.dg.wss.aps.service.IApsActivityService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Api(tags = "aps活动")
@RestController
@RequestMapping("/aps/apsActivity")
public class ApsActivityController extends BaseController {
    @Autowired
    private IApsActivityService apsActivityService;

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<ApsActivityVo> list = apsActivityService.pageByMap(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps活动", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ApsActivity apsActivity) {
        apsActivity.setCreateBy(getUserId());
        apsActivity.setCreateTime(new Date());
        return toAjax(apsActivityService.insert(apsActivity));
    }

    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps活动", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody ApsActivity apsActivity) {
        apsActivity.setUpdateBy(getUserId());
        apsActivity.setUpdateTime(new Date());
        return toAjax(apsActivityService.update(apsActivity));
    }

    @ApiOperation(value = "查询by id")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(apsActivityService.selectById(id));
    }

    @ApiOperation(value = "删除")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps活动", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(apsActivityService.delete(id));
    }
}