package com.dg.wss.web.controller.application;

import com.dg.wss.application.domain.AppPageComponent;
import com.dg.wss.application.vo.AppFormQueryVo;
import com.dg.wss.application.vo.AppPageComponentQueryVo;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.validate.CreateGroup;
import com.dg.wss.common.core.validate.UpdateGroup;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.application.service.IAppFormService;
import com.dg.wss.application.vo.AppFormVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;

@Api(tags = "表单管理")
@RestController
@RequestMapping("/form")
public class AppFormController extends BaseController {

    @Autowired
    private IAppFormService formService;

    @ApiOperation(value = "创建表单")
    @ApiOperationSupport(ignoreParameters = {"id", "deleteFlag", "createTime", "updateTime", "createBy", "updateBy"})
    @PostMapping("/add")
    @RepeatSubmit
    @Log(title = "表单新增", businessType = BusinessType.INSERT)
    public AjaxResult createForm(@RequestBody @Validated({Default.class,CreateGroup.class}) AppFormVo appFormVo) {
        formService.createAppPageComponent(appFormVo);
        return AjaxResult.success("表单创建成功", appFormVo);
    }

    @ApiOperation(value = "修改表单")
    @ApiOperationSupport(ignoreParameters = {"deleteFlag", "createTime", "updateTime", "createBy", "updateBy"})
    @PutMapping("/update")
    @RepeatSubmit
    @Log(title = "表单更新", businessType = BusinessType.UPDATE)
    public AjaxResult updateForm(@RequestBody @Validated({Default.class, UpdateGroup.class}) AppFormVo appFormVo) {
        formService.updateAppPageComponent(appFormVo);
        return AjaxResult.success("表单更新成功",appFormVo);
    }

    @ApiOperation(value = "删除表单")
    @DeleteMapping("/remove/{id}")
    @RepeatSubmit
    @Log(title = "表单删除", businessType = BusinessType.DELETE)
    public AjaxResult deleteForm(@PathVariable @NotNull(message = "表单ID不能为空") Long id) {
        formService.deleteAppPageComponentById( id);
        return AjaxResult.success("表单删除成功");
    }

    @DeleteMapping("removeByKey/{key}")
    @ApiOperation("根据组件key删除表单")
    @Log(title = "根据组件key删除表单", businessType = BusinessType.DELETE)
    public AjaxResult remove(@PathVariable @NotNull(message = "表单Key不能为空") String key) {
        formService.deleteAppPageComponentByKey(key);
        return AjaxResult.success("删除表单成功");
    }

    @GetMapping("/list")
    @ApiOperation("分页查询表单列表")
    public TableDataInfo listForms(AppFormQueryVo queryVo) {
        startPage();
        return getDataTable(formService.listAppPageComponents(queryVo));
    }

    @GetMapping("/getInfo")
    @ApiOperation("获取表单详情")
    public AjaxResult getFormDetails(@ApiParam("表单ID") @RequestParam @NotNull(message = "表单ID不能为空") Long id) {
        AppPageComponent form = formService.getAppPageComponentById( id);
        return AjaxResult.success("获取表单成功",form);
    }
}
