package com.dg.wss.web.controller.all;

import com.dg.wss.all.domain.AllRecord;
import com.dg.wss.all.service.IAllRecordService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 删除记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@RestController
@RequestMapping("/all/allRecord")
public class AllRecordController extends BaseController {
    @Autowired
    private IAllRecordService allRecordService;


    /**
     * 查询删除记录列表
     */
    @ApiOperation(value = "查询删除记录列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo list(@ModelAttribute AllRecord allRecord) {
        fillTime(allRecord);
        startPage();
        List<AllRecord> list = allRecordService.listAllRecord(allRecord);
        return getDataTable(list);
    }

    /**
     * 查询删除记录详情
     */
    @ApiOperation(value = "查询删除记录详情")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult list(@RequestParam Long id) {
        return AjaxResult.success(allRecordService.getById(id));
    }

    /**
    * 新增删除记录信息
    */
    @ApiOperation(value = "新增删除记录信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "删除记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@RequestBody AllRecord allRecord){
        allRecord.setCreateBy(getUserId());
        allRecord.setCircleId(getCircleId());
        allRecord.setCreateTime(new Date());
        return AjaxResult.success(allRecordService.save(allRecord));
    }

    /**
    * 批量保存删除记录
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "删除记录", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAdd")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchAdd(@RequestBody List<AllRecord> allRecords) {
        for (AllRecord allRecord : allRecords) {
            allRecord.setCircleId(getCircleId());
            allRecord.setCreateBy(getUserId());
            allRecord.setCreateTime(new Date());
        }
        return AjaxResult.success(allRecordService.saveBatch(allRecords));
    }

    /**
     * 修改删除记录信息
     */
    @ApiOperation(value = "修改删除记录信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "删除记录", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@RequestBody AllRecord allRecord){
        allRecord.setCircleId(getCircleId());
        return AjaxResult.success(allRecordService.updateById(allRecord));
    }

    /**
    * 批量修改删除记录
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "删除记录", businessType = BusinessType.UPDATE)
    @PutMapping("/batchEdit")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchEdit(@RequestBody List<AllRecord> allRecords) {
        for (AllRecord allRecord : allRecords) {
            allRecord.setCircleId(getCircleId());
        }
        return AjaxResult.success(allRecordService.updateBatchById(allRecords));
    }

    /**
     * 删除删除记录信息
     */
    @ApiOperation(value = "删除删除记录信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "删除记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable String id){
        return AjaxResult.success(allRecordService.removeById(id));
    }
}
