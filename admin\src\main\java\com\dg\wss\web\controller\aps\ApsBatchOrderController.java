package com.dg.wss.web.controller.aps;

import com.dg.wss.aps.domain.ApsBatchOrder;
import com.dg.wss.aps.domain.ApsLot;
import com.dg.wss.aps.service.IApsBatchOrderService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;
import java.util.Map;
@Api(tags = "aps批单")
@RestController
@RequestMapping("/aps/apsBatchOrder")
public class ApsBatchOrderController extends BaseController {
    @Autowired
    private IApsBatchOrderService apsBatchOrderService;
    
    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
                 @ApiImplicitParam(paramType = "query", name = "id", value = "主键ID", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "code", value = "编码", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "contractId", value = "订单", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "productId", value = "产品ID", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "contractProductId", value = "父级销单id", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "status", value = "0初始，1已完成", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "type", value = "1应交付", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "note", value = "产品补充描述", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "productNum", value = "产品数量", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "productPrice", value = "产品单价", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "deliverNum", value = "产品已交付数量", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createBy", value = "创建人，录入人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createTime", value = "创建时间", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateBy", value = "更新人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateTime", value = "更新时间", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "deliverDate", value = "交付时间", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "periodType", value = "产品交付类型", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "customerNo", value = "客户料号", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "moq", value = "最小起订量", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "tag", value = "1加急 0普通", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "mrpCompute", value = "1已计算", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "warehouseId", value = "仓库id", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "issueNum", value = "已发料数量", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "stockInNum", value = "0还没入库，1已入库", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "completedNum", value = "已交易数量加总", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "processId", value = "工艺id", dataType = "Sting"),
    })
    @DynamicResponseParameters(name = "apsBatchOrder_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = ApsBatchOrder[].class)
    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        map.put("organizationId",getOrganizationId());
        List<ApsBatchOrder> list = apsBatchOrderService.pageByMap(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "mrp待计算的批单列表")
    @GetMapping("/saleDetail")//apsBatchOrder/saleDetail?
    public TableDataInfo saleDetail(@ApiIgnore @RequestParam Map<String, Object> map) {
//        startPage();
        fillTimeForMap(map);
        map.put("circleId",getCircleId());
        map.put("organizationId",getOrganizationId());
        return getDataTable(apsBatchOrderService.saleDetail(map));
    }

    @ApiOperation(value = "根据销单查询批单")
    @GetMapping("/listByContractProduct")
    public TableDataInfo listByContractProduct(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        map.put("organizationId",getOrganizationId());
        return getDataTable(apsBatchOrderService.listByContractProduct(map));
    }

    @ApiOperation(value = "查询交期在某个时间之后的已排批单")
    @GetMapping("/listPlanedBatchOrder")
    public TableDataInfo listPlanedBatchOrder(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        map.put("organizationId",getOrganizationId());
        return getDataTable(apsBatchOrderService.listPlanedBatchOrder(map));
    }

    @ApiOperation(value = "总的批单列表，分页，附带已完成lot、单数")
    @GetMapping("/listWithLotNum")//apsBatchOrder/listWithLotNum
    public TableDataInfo listWithLotNum(@ApiIgnore @RequestParam Map<String, Object> map) {
        map.put("circleId",getCircleId());
        if(!map.containsKey("useDefaultOrganizationId")){
            map.put("organizationId",getOrganizationId());
        }
        startPage();
        return getDataTable(apsBatchOrderService.listWithLotNum(map));
    }
    @ApiOperation(value = "总的批单列表分单统计")
    @GetMapping("/listWithLotNumStat")//apsBatchOrder/listWithLotNumStat
    public AjaxResult listWithLotNumStat(@ApiIgnore @RequestParam Map<String, Object> map) {
        map.put("circleId",getCircleId());
        if(!map.containsKey("useDefaultOrganizationId")){
            map.put("organizationId",getOrganizationId());
        }
        return AjaxResult.success(apsBatchOrderService.listWithLotNumStat(map));
    }

    @ApiOperation(value = "重排批单")
    @Log(title = "重排批单", businessType = BusinessType.INSERT)
    @PutMapping("/rePlanBatchOrder")
    public AjaxResult rePlanBatchOrder(@ApiIgnore @RequestBody Map<String, Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        map.put("organizationId",getOrganizationId());
        map.put("userId",getUserId());
        apsBatchOrderService.rePlanBatchOrder(map);
        return AjaxResult.success();
    }

    @ApiOperation(value = "模拟排批单")
    @Log(title = "模拟排批单", businessType = BusinessType.INSERT)
    @PostMapping("/simulationPlanBatchOrder")
    public AjaxResult simulationPlanBatchOrder(@ApiIgnore @RequestBody Map<String, Object> map)  {
        startPage();
        map.put("circleId",getCircleId());
        map.put("userId",getUserId());
        apsBatchOrderService.simulationPlanBatchOrder(map);
        return AjaxResult.success();
    }

    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsBatchOrder_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps批单", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ApsBatchOrder apsBatchOrder) {
        apsBatchOrder.setCreateBy(getUserId());
        apsBatchOrder.setCreateTime(new Date());
        apsBatchOrder.setCircleId(getCircleId());
        apsBatchOrder.setOrganizationId(getOrganizationId());
        return toAjax(apsBatchOrderService.insert(apsBatchOrder));
    }
    
    @ApiOperation(value = "销单分批，添加多个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "collateralRate_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @RepeatSubmit
    @PostMapping("/batchInsert")
    public AjaxResult batchInsert(@RequestBody List<ApsBatchOrder> apsBatchOrders) {
        return toAjax(apsBatchOrderService.batchInsert(apsBatchOrders, getUserId()));
    }

    @ApiOperation(value = "批单合并")
    @Log(title = "批单合并", businessType = BusinessType.INSERT)
    @PostMapping("/merge")
    public AjaxResult merge(@RequestBody ApsLot lot) {
        return toAjax(apsBatchOrderService.merge(lot.getIdList()));
    }

    @ApiOperation(value = "批单拆分")
    @Log(title = "批单拆分", businessType = BusinessType.INSERT)
    @PostMapping("/split")
    public AjaxResult split(@RequestBody List<ApsBatchOrder> apsBatchOrders,@RequestParam Long batchOrderId ) {
        return toAjax(apsBatchOrderService.split(batchOrderId, apsBatchOrders,getUserId()));
    }
    
    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsBatchOrder_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps批单", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody ApsBatchOrder apsBatchOrder) {
        apsBatchOrder.setUpdateBy(getUserId());
        apsBatchOrder.setUpdateTime(new Date());
        return toAjax(apsBatchOrderService.update(apsBatchOrder));
    }

    @ApiOperation(value = "修改批单和对应的lot、三单交期")
    @Log(title = "修改批单和对应的lot、三单交期", businessType = BusinessType.UPDATE)
    @PutMapping("/editDeliverDate")
    public AjaxResult editDeliverDate(@RequestBody ApsBatchOrder apsBatchOrder) {
        apsBatchOrder.setUpdateBy(getUserId());
        apsBatchOrder.setUpdateTime(new Date());
        return toAjax(apsBatchOrderService.editDeliverDate(apsBatchOrder));
    }

    @ApiOperation(value = "批单挂起，对应的lot标记为挂起")
    @Log(title = "批单挂起，对应的lot标记为挂起", businessType = BusinessType.UPDATE)
    @PutMapping("/editPlaned")
    public AjaxResult editPlaned(@RequestBody ApsBatchOrder apsBatchOrder) {
        apsBatchOrder.setUpdateBy(getUserId());
        apsBatchOrder.setUpdateTime(new Date());
        return toAjax(apsBatchOrderService.editPlaned(apsBatchOrder));
    }
    @ApiOperation(value = "修改批单供应单位Id，对应的lot供应单位也修改")
    @Log(title = "修改批单供应单位Id，对应的lot供应单位也修改", businessType = BusinessType.UPDATE)
    @PutMapping("/editOrganizationId")
    public AjaxResult editOrganizationId(@RequestBody ApsBatchOrder apsBatchOrder) {
        apsBatchOrder.setUpdateBy(getUserId());
        apsBatchOrder.setUpdateTime(new Date());
        return toAjax(apsBatchOrderService.editOrganizationId(apsBatchOrder));
    }
    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ApsBatchOrder.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(apsBatchOrderService.selectById(id));
    }

//    @GetMapping("/generatePdf")
//    public AjaxResult generatePdf(@ApiParam("ID") @RequestBody ApsBatchOrder apsBatchOrder )  {
//        return AjaxResult.success(apsBatchOrderService.generatePdf(apsBatchOrder.getContractIdList()));
//    }
    
    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "apsBatchOrder_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps批单", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(apsBatchOrderService.delete(id));
    }
}