package com.dg.wss.web.controller.attendance;

import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.common.utils.StringUtils;
import com.dg.wss.commonservice.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestBody;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.attendance.domain.AttendanceOutColleague;
import com.dg.wss.attendance.service.IAttendanceOutColleagueService;
import com.dg.wss.common.core.domain.AjaxResult;



/**
 * 外出同行人Controller
 * 
 * <AUTHOR>
 * @date 2024-12-18
 */
@Controller
@Api(tags = "外出同行人")
@RequestMapping("/attendance/colleague")
public class AttendanceOutColleagueController extends BaseController
{
    private String prefix = "attendance/colleague";

    @Autowired
    private IAttendanceOutColleagueService attendanceOutColleagueService;

    /**
     * 查询外出同行人列表
     */
    @ApiOperation(value = "查询外出同行人列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(@RequestBody AttendanceOutColleague attendanceOutColleague)
    {
        startPage();
        List<AttendanceOutColleague> list = attendanceOutColleagueService.selectAttendanceOutColleagueList(attendanceOutColleague);
        return getDataTable(list);
    }

    /**
     * 查询外出同行人
     */
    @ApiOperation(value = "查询外出同行人")
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/getById")
    @ResponseBody
    public AjaxResult getById(@RequestParam(name = "id",required = true) Long id)
    {
        return AjaxResult.success(attendanceOutColleagueService.selectAttendanceOutColleagueById(id));
    }

    /**
     * 导出外出同行人列表
     */
    //@RequiresPermissions("attendance:colleague:export")
    @ApiOperation(value = "导出外出同行人列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "外出同行人", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(AttendanceOutColleague attendanceOutColleague)
    {
        List<AttendanceOutColleague> list = attendanceOutColleagueService.selectAttendanceOutColleagueList(attendanceOutColleague);
        ExcelUtil<AttendanceOutColleague> util = new ExcelUtil<AttendanceOutColleague>(AttendanceOutColleague.class);
        return util.exportExcel(list, "外出同行人数据");
    }

    /**
     * 新增保存外出同行人
     */
    @ApiOperation(value = "新增保存外出同行人")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "外出同行人", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody AttendanceOutColleague attendanceOutColleague)
    {
        return toAjax(attendanceOutColleagueService.insertAttendanceOutColleague(attendanceOutColleague));
    }

    /**
     * 修改保存外出同行人
     */
    @ApiOperation(value = "修改保存外出同行人")
    //@RequiresPermissions("attendance:colleague:edit")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "外出同行人", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody AttendanceOutColleague attendanceOutColleague)
    {
        return toAjax(attendanceOutColleagueService.updateAttendanceOutColleague(attendanceOutColleague));
    }

    /**
     * 删除外出同行人
     */
    //@RequiresPermissions("attendance:colleague:remove")
    @ApiOperation(value = "删除外出同行人,ids,以逗号分割")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "外出同行人", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(@RequestParam(value = "ids", required = true)  String ids)
    {
        return toAjax(attendanceOutColleagueService.deleteAttendanceOutColleagueByIds(ids));
    }
}
