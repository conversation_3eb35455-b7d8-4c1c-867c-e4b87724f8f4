package com.dg.wss.web.controller.attendance;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.dg.wss.attendance.domain.AttendanceDevice;
import com.dg.wss.attendance.domain.DeviceUser;
import com.dg.wss.attendance.domain.GroupDevice;
import com.dg.wss.attendance.domain.vo.AttendanceDeviceVo;
import com.dg.wss.attendance.service.IAttendanceDeviceService;
import com.dg.wss.attendance.service.IDeviceUserService;
import com.dg.wss.attendance.service.IGroupDeviceService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.common.utils.weifengqi.AESUtil;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Api(tags = "考勤机")
@RestController
@RequestMapping("/attendance/device")
public class AttendanceDeviceController extends BaseController {
    @Autowired
    private IAttendanceDeviceService attendanceDeviceService;
    @Autowired
    private IGroupDeviceService groupDeviceService;
    @Autowired
    private IDeviceUserService deviceUserService;

    @ApiOperation(value = "添加考勤机")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime"})
    @DynamicResponseParameters(name = "attendanceDevice_insert", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/insert")
    @RepeatSubmit
    @Log(title = "考勤机", businessType = BusinessType.INSERT)
    public AjaxResult insert(@Validated @RequestBody AttendanceDevice device) {
        device.setUpdateBy(getUserId());
        device.setCreateTime(new Date());
        return toAjax(attendanceDeviceService.deviceHeart(device));
    }

    @ApiOperation(value = "绑定考勤机")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime"})
    @DynamicResponseParameters(name = "attendanceDevice_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/add")
    @RepeatSubmit
    @Log(title = "考勤机绑定", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated @RequestBody GroupDevice groupDevice) {
        groupDevice.setCreateBy(getUserId());
        groupDevice.setCircleId(getCircleId());
        return toAjax(groupDeviceService.addGroupDevice(groupDevice));
    }

    @ApiOperation(value = "删除考勤机绑定")
    @DynamicResponseParameters(name = "attendanceAddress_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @DeleteMapping("/remove/{deviceId}/{groupId}")
    @RepeatSubmit
    @Log(title = "考勤机绑定", businessType = BusinessType.DELETE)
    public AjaxResult delete(@PathVariable String deviceId, @PathVariable Long groupId) {
        return toAjax(groupDeviceService.deleteGroupDevice(deviceId, groupId));
    }

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", name = "circleId", value = "圈子ID", dataType = "Sting")})
    @DynamicResponseParameters(name = "attendanceDevice_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = AttendanceDeviceVo[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> map) {
        startPage();
        return getDataTable(attendanceDeviceService.page(map));
    }

    @ApiOperation(value = "id查询")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AttendanceDeviceVo.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("id") @RequestParam Long id) {
        return checkCircle(attendanceDeviceService.getInfo(id));
    }

    @ApiOperation(value = "考勤机绑定圈子")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime", "name", "avatar", "late", "early", "lack", "trip", "clock", "avgWork", "sumOverTime"})
    @DynamicResponseParameters(name = "attendanceDevice_edit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@Validated @RequestBody AttendanceDevice device) {
        synchronized (this) {
            return toAjax(attendanceDeviceService.editAttendanceDevice(device));
        }
    }

    @ApiOperation(value = "考勤机解绑圈子")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime", "name", "avatar", "late", "early", "lack", "trip", "clock", "avgWork", "sumOverTime"})
    @DynamicResponseParameters(name = "attendanceDevice_cancelCircle", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/cancelCircle")
    @RepeatSubmit
    public AjaxResult cancelCircle(@Validated @RequestBody AttendanceDevice device) {
        return toAjax(attendanceDeviceService.editAttendanceDevice(device));
    }

    @ApiOperation(value = "设备分页查询")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", name = "groupId", value = "考勤组", dataType = "Long")})
    @DynamicResponseParameters(name = "attendanceDevice_deviceList", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = GroupDevice[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/deviceList")
    public TableDataInfo deviceList(@RequestParam Map<String, Object> map) {
        startPage();
        filterCircle(map);
        return getDataTable(groupDeviceService.list(map));
    }

    @ApiOperation(value = "设备已添加人数")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", name = "groupId", value = "考勤组", dataType = "Long")})
    @DynamicResponseParameters(name = "attendanceDevice_selectByGroupId", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = DeviceUser[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/selectByGroupId")
    public AjaxResult selectByGroupId(@RequestParam(required = false) Long groupId) {
        return deviceUserService.selectByGroupId(getCircleId(), groupId);
    }

    @ApiOperation(value = "考勤机鉴权")
    @DynamicResponseParameters(name = "attendanceDevice_authentication", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = Map.class)})
    @GetMapping("/authentication")
    @RepeatSubmit
    public AjaxResult authentication(@RequestParam Long time, @RequestParam String model, @RequestParam String uid) {
        Date now = new Date();
        time = time * 1000;
        log.debug(DateUtil.format(new Date(time), "yy-MM-dd HH:mm:ss"));
        if (now.getTime() - time < 5000) {//相差不超5秒
            String str1 = AESUtil.decryptWithIv(uid, AESUtil.request_key, AESUtil.request_iv);//解密
            try {
                String dateStr = str1.substring(0, 6) + str1.substring(str1.length() - 6);
                Date date = DateUtil.offsetHour(DateUtil.parse(dateStr, "yyMMddHHmmss"), 8);//8小时之前
                if (date.getTime() == time)
                    if (DateUtil.between(date, now, DateUnit.SECOND) <= 5) {//相差不超5秒
                        String deviceId = str1.substring(6, str1.length() - 6);
                        attendanceDeviceService.deviceHeart(new AttendanceDevice(deviceId, model, 1));
                        Map<String, String> result = new HashMap<>();
                        result.put("server", SpringUtil.getProperty("spring.mqtt.attendance.deviceUrl"));
                        result.put("port", SpringUtil.getProperty("spring.mqtt.attendance.wsPort"));
                        result.put("u", SpringUtil.getProperty("spring.mqtt.attendance.username"));
                        result.put("p", AESUtil.encrypt(SpringUtil.getProperty("spring.mqtt.attendance.password")));
                        result.put("a", AESUtil.encrypt("9nKCkjiRh4SexbwC5PyoVfGrzPEQfeesGojwm1VSBFxn"));
                        result.put("k", AESUtil.encrypt("DmkmwYJM9kjZRP4kihV3QcBSEU99Wi94w1sS58wvRGaY"));
                        return AjaxResult.success(result);
                    }
            } catch (Exception e) {
                log.error("鉴权失败", e);
            }
        }
        return AjaxResult.error();
    }
}
