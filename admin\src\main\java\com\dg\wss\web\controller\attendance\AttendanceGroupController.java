package com.dg.wss.web.controller.attendance;

import com.dg.wss.attendance.domain.AttendanceGroup;
import com.dg.wss.attendance.domain.GroupUser;
import com.dg.wss.attendance.domain.GroupUserHistory;
import com.dg.wss.attendance.domain.vo.AttendanceGroupVo;
import com.dg.wss.attendance.service.IAttendanceGroupService;
import com.dg.wss.attendance.service.IGroupUserHistoryService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.common.exception.GlobalException;
import com.dg.wss.common.utils.bean.BeanUtils;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "考勤组")
@RestController
@RequestMapping("/attendance/attendanceGroup")
public class AttendanceGroupController extends BaseController {
    @Autowired
    private IAttendanceGroupService attendanceGroupService;
    @Autowired
    private IGroupUserHistoryService groupUserHistoryService;

    @ApiOperation(value = "编辑考勤组")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime"})
    @DynamicResponseParameters(name = "attendanceGroup_edit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/edit")
    @RepeatSubmit
    @Log(title = "考勤组", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated @RequestBody AttendanceGroupVo attendanceGroup) {
        synchronized (this) {
            attendanceGroup.setCircleId(getCircleId());
            return attendanceGroupService.editAttendanceGroup(attendanceGroup);
        }
    }

    @ApiOperation(value = "删除考勤组")
    @DynamicResponseParameters(name = "attendanceGroup_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @DeleteMapping("/remove")
    @RepeatSubmit
    @Log(title = "考勤组", businessType = BusinessType.DELETE)
    public AjaxResult delete(@Validated Long id) {
        synchronized (this) {
            return attendanceGroupService.deleteAttendanceGroup(id);
        }
    }

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "Sting")})
    @DynamicResponseParameters(name = "attendanceGroup_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = AttendanceGroupVo[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> map) {
        filterCircle(map);
        startPage();
        return getDataTable(attendanceGroupService.list(map));
    }

    @ApiOperation(value = "id查询")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AttendanceGroupVo.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("id") @RequestParam Long id) {
        return checkCircle(attendanceGroupService.getInfo(id));
    }

    @ApiOperation(value = "查询用户所在考勤组，指定日期查询，返回历史考勤组，同时返回当前日期的考勤组")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AttendanceGroup.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getGroupByUserAttendanceDay")
    public AjaxResult getGroupByUser(@ApiParam("用户id") @RequestParam(required = true) Long userId,
                                     @ApiParam("指定考勤日期") @RequestParam(required = true) Date attendanceDay) {
        List<AttendanceGroup> groupList = new ArrayList<>();

        AttendanceGroup attendanceGroup = attendanceGroupService.getGroupByUser(userId,getCircleId());
        if (attendanceGroup == null) {
            return AjaxResult.error("用户没有考勤组");
        }
        groupList.add(attendanceGroup);

        GroupUserHistory groupUserHistory = groupUserHistoryService.getGroupByUserIdCircleIdAndDate(userId,getCircleId(),attendanceDay);
        if(groupUserHistory!=null){
            AttendanceGroup historyAttendanceGroup = attendanceGroupService.getById(groupUserHistory.getGroupId());

            if(historyAttendanceGroup!=null){
                groupList.add(historyAttendanceGroup);
            }
        }

        return AjaxResult.success("获取历史和当前考勤组成功", groupList);
    }

    @ApiOperation(value = "查询用户所在考勤组")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AttendanceGroup.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getGroupByUser")
    public AjaxResult getGroupByUser(@ApiParam("用户id") @RequestParam Long userId) {
        return AjaxResult.success(attendanceGroupService.getGroupByUser(userId, getCircleId()));
    }

    @ApiOperation(value = "查询管理员所在考勤组")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AttendanceGroup[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getGroupByManager")
    public AjaxResult getGroupByManager(@ApiParam("用户id") @RequestParam Long userId) {
        return AjaxResult.success(attendanceGroupService.getGroupByManager(userId, getCircleId()));
    }

    @ApiOperation(value = "复制一个考勤组的排班到其他考勤组")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/copyAttendance")
    @Log(title = "考勤组", businessType = BusinessType.UPDATE)
    public AjaxResult copyAttendance(@ApiParam("考勤组id") @RequestParam Long groupId, @ApiParam("被覆盖的考勤组id") @RequestParam List<Long> ids) {
        if (!ids.isEmpty()) {
            ids = ids.stream().distinct().collect(Collectors.toList());
            int index = ids.indexOf(groupId);
            if (index != -1) ids.remove(index);
        }
        if (ids.isEmpty()) return AjaxResult.error("请选中要覆盖的考勤组");
        synchronized (this) {
            attendanceGroupService.copyAttendance(getCircleId(), getUserId(), groupId, ids);
        }
        return AjaxResult.success();
    }

}
