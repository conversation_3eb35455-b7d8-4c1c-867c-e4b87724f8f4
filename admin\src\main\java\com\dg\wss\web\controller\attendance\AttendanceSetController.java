package com.dg.wss.web.controller.attendance;

import com.dg.wss.attendance.domain.AttendanceBlock;
import com.dg.wss.attendance.domain.AttendanceSet;
import com.dg.wss.attendance.domain.vo.AttendanceBlockVo;
import com.dg.wss.attendance.service.IAttendanceBlockService;
import com.dg.wss.attendance.service.IAttendanceSetService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "考勤个人设置")
@RestController
@RequestMapping("/attendance/attendanceSet")
public class AttendanceSetController extends BaseController {
    @Autowired
    private IAttendanceSetService attendanceSetService;
    @Autowired
    private IAttendanceBlockService attendanceBlockService;

    @ApiOperation(value = "添加考勤个人设置")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime"})
    @DynamicResponseParameters(name = "attendanceSet_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/add")
    @RepeatSubmit
    @Log(title = "考勤个人设置", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated @RequestBody AttendanceSet attendanceSet) {
        attendanceSet.setUserId(getUserId());
        return toAjax(attendanceSetService.addAttendanceSet(attendanceSet));
    }

    @ApiOperation(value = "编辑考勤个人设置")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime"})
    @DynamicResponseParameters(name = "attendanceSet_edit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/edit")
    @RepeatSubmit
    @Log(title = "考勤个人设置", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated @RequestBody AttendanceSet attendanceSet) {
        return toAjax(attendanceSetService.editAttendanceSet(attendanceSet));
    }

    @ApiOperation(value = "查询用户考勤个人设置")
    @DynamicResponseParameters(name = "attendanceSet_getAttendanceSet", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AttendanceSet.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getAttendanceSet")
    public AjaxResult getAttendanceSet() {
        return AjaxResult.success(attendanceSetService.getAttendanceSet(getUserId()));
    }

    @ApiOperation(value = "覆盖屏蔽人员")
    @DynamicResponseParameters(name = "attendanceBlock_insert", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @DeleteMapping("/insertBlock")
    @RepeatSubmit
    @Log(title = "考勤黑名单", businessType = BusinessType.INSERT)
    public AjaxResult insertBlock(@RequestBody List<AttendanceBlock> list) {
        return toAjax(attendanceBlockService.batchInsert(list, getCircleId(), getUserId()));
    }

    @ApiOperation(value = "分页查询屏蔽人员")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "Sting")})
    @DynamicResponseParameters(name = "attendanceBlock_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = AttendanceBlockVo[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/blockList")
    public TableDataInfo list(@RequestParam Map<String, Object> map) {
        filterCircle(map);
        startPage();
        return getDataTable(attendanceBlockService.list(map));
    }
}
