package com.dg.wss.web.controller.base;


import com.dg.wss.base.domain.RangeConfig;
import com.dg.wss.base.service.IRangeConfigService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.constant.RangeConfigTypeConstant;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "范围参数")
@RestController
@RequestMapping("/base/rangeConfig")
public class RangeConfigController extends BaseController {

    @Autowired
    private IRangeConfigService rangeConfigService;

    @ApiOperation(value = "添加发票范围参数")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "发票管理", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('base:rangeConfig:addInvoice')")
    @PostMapping("/addInvoice")
    @RepeatSubmit
    public AjaxResult addInvoice(@Validated @RequestBody List<RangeConfig> rangeConfig) {
        for (RangeConfig rangeConfig1 : rangeConfig) {
            rangeConfig1.setTypeGroup(RangeConfigTypeConstant.INVOICE_SETTING);
            rangeConfig1.setCreateBy(getUsername());
        }
        return toAjax(rangeConfigService.insertRangeConfig(rangeConfig));
    }

    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "发票管理", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('base:rangeConfig:editInvoice')")
    @PutMapping("/editInvoice")
    @RepeatSubmit
    public AjaxResult editInvoice(@Validated @RequestBody List<RangeConfig> rangeConfig) {
        for (RangeConfig rangeConfig1 : rangeConfig) {
            rangeConfig1.setUpdateBy(getUsername());
        }
        return toAjax(rangeConfigService.updateRangeConfig(rangeConfig));
    }

    @ApiOperation(value = "分页查询发票范围设置")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", name = "pid", value = "父类ID", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "类型", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "name", value = "类型", dataType = "String")
    })
    @DynamicResponseParameters(name = "RangeConfig_listInvoice", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = RangeConfig[].class)})
    @PreAuthorize("@ss.hasPermi('base:config:listInvoice')")
    @GetMapping("/listInvoice")
    public TableDataInfo listInvoice(@RequestParam Map<String, Object> map) {
        startPage();
        map.put("typeGroup", RangeConfigTypeConstant.INVOICE_SETTING);
        List<RangeConfig> list = rangeConfigService.selectRangeConfigList(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "id查询发票范围设置")
    @DynamicResponseParameters(name = "RangeConfig_getInfoInvoice", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = RangeConfig.class)})
    @PreAuthorize("@ss.hasPermi('base:config:getInfoInvoice')")
    @GetMapping("/getInfoInvoice")
    public AjaxResult getInfoInvoice(Long id) {
        return AjaxResult.success(rangeConfigService.getRangeConfigById(id));
    }

    @ApiOperation(value = "根据类型，获取发票范围设置")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", name = "pid", value = "父类ID", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "类型", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "name", value = "类型", dataType = "String")
    })
    @DynamicResponseParameters(name = "RangeConfig_getTypeInvoice", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = RangeConfig.class)})
    @PreAuthorize("@ss.hasPermi('base:config:getTypeInvoice')")
    @GetMapping("/getTypeInvoice")
    public TableDataInfo getTypeInvoice(String type) {
        startPage();
        List<RangeConfig> list = rangeConfigService.getTypeInvoice(type, RangeConfigTypeConstant.INVOICE_SETTING);
        return getDataTable(list);
    }

    @ApiOperation(value = "删除发票范围设置")
    @Log(title = "发票管理", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('base:config:removeInvoice')")
    @DeleteMapping("/removeInvoice/{id}")
    public AjaxResult removeInvoice(@PathVariable Long[] id) {
        return id.length > 0 ? toAjax(rangeConfigService.deleteRangeConfig(id)) : AjaxResult.error();
    }

    @ApiOperation(value = "添加提现设置")
    @Log(title = "提现设置", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('base:rangeConfig:addWithdraw')")
    @PostMapping("/addWithdraw")
    @RepeatSubmit
    public AjaxResult addWithdraw(@Validated @RequestBody List<RangeConfig> rangeConfig) {
        for (RangeConfig rangeConfig1 : rangeConfig) {
            rangeConfig1.setTypeGroup(RangeConfigTypeConstant.WITHDRAW_SETTING);
            rangeConfig1.setCreateBy(getUsername());
        }
        return toAjax(rangeConfigService.insertRangeConfig(rangeConfig));
    }

    @ApiOperation(value = "修改提现设置")
    @Log(title = "提现设置", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('base:rangeConfig:editWithdraw')")
    @PutMapping("/editWithdraw")
    @RepeatSubmit
    public AjaxResult editWithdraw(@Validated @RequestBody List<RangeConfig> rangeConfig) {
        for (RangeConfig rangeConfig1 : rangeConfig) {
            rangeConfig1.setUpdateBy(getUsername());
        }
        return toAjax(rangeConfigService.updateRangeConfig(rangeConfig));
    }

    @ApiOperation(value = "分页查询提现设置")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", name = "pid", value = "父类ID", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "类型", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "name", value = "类型", dataType = "String")
    })
    @DynamicResponseParameters(name = "RangeConfig_listWithdraw", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = RangeConfig[].class)})
    @PreAuthorize("@ss.hasPermi('base:config:listWithdraw')")
    @GetMapping("/listWithdraw")
    public TableDataInfo listWithdraw(@RequestParam Map<String, Object> map) {
        startPage();
        map.put("typeGroup", RangeConfigTypeConstant.WITHDRAW_SETTING);
        List<RangeConfig> list = rangeConfigService.selectRangeConfigList(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "id查询提现设置")
    @DynamicResponseParameters(name = "RangeConfig_getInfoWithdraw", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = RangeConfig.class)})
    @PreAuthorize("@ss.hasPermi('base:config:getInfoWithdraw')")
    @GetMapping("/getInfoWithdraw")
    public AjaxResult getInfoWithdraw(Long id) {
        return AjaxResult.success(rangeConfigService.getRangeConfigById(id));
    }

    @ApiOperation(value = "删除提现设置")
    @Log(title = "提现设置", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('base:config:removeWithdraw')")
    @DeleteMapping("/removeWithdraw/{id}")
    public AjaxResult removeWithdraw(@PathVariable Long[] id) {
        return id.length > 0 ? toAjax(rangeConfigService.deleteRangeConfig(id)) : AjaxResult.error();
    }


    @ApiOperation(value = "添加标签设置")
    @Log(title = "标签设置", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('base:rangeConfig:addLabel')")
    @PostMapping("/addLabel")
    @RepeatSubmit
    public AjaxResult addLabel(@Validated @RequestBody List<RangeConfig> rangeConfig) {
        for (RangeConfig rangeConfig1 : rangeConfig) {
            rangeConfig1.setTypeGroup(RangeConfigTypeConstant.LABEL_SETTING);
            rangeConfig1.setCreateBy(getUsername());
        }
        return toAjax(rangeConfigService.insertRangeConfig(rangeConfig));
    }

    @ApiOperation(value = "修改标签设置")
    @Log(title = "标签设置", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('base:rangeConfig:editLabel')")
    @PutMapping("/editLabel")
    @RepeatSubmit
    public AjaxResult editLabel(@Validated @RequestBody List<RangeConfig> rangeConfig) {
        for (RangeConfig rangeConfig1 : rangeConfig) {
            rangeConfig1.setUpdateBy(getUsername());
        }
        return toAjax(rangeConfigService.updateRangeConfig(rangeConfig));
    }

    @ApiOperation(value = "分页查询标签设置")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", name = "pid", value = "父类ID", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "类型", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "name", value = "类型", dataType = "String")
    })
    @DynamicResponseParameters(name = "RangeConfig_listLabel", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = RangeConfig.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listLabel")
    public TableDataInfo listLabel(@RequestParam Map<String, Object> map) {
        startPage();
        map.put("typeGroup", RangeConfigTypeConstant.LABEL_SETTING);
        List<RangeConfig> list = rangeConfigService.selectRangeConfigList(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "id查询标签设置")
    @DynamicResponseParameters(name = "RangeConfig_getInfoLabel", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = RangeConfig.class)})
    @PreAuthorize("@ss.hasPermi('base:config:getInfoLabel')")
    @GetMapping("/getInfoLabel")
    public AjaxResult getInfoLabel(Long id) {
        return AjaxResult.success(rangeConfigService.getRangeConfigById(id));
    }

    @ApiOperation(value = "删除标签设置")
    @Log(title = "标签设置", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('base:config:removeLabel')")
    @DeleteMapping("/removeLabel/{id}")
    public AjaxResult removeLabel(@PathVariable Long[] id) {
        return id.length > 0 ? toAjax(rangeConfigService.deleteRangeConfig(id)) : AjaxResult.error();
    }

    @ApiOperation(value = "添加咨询设置")
    @Log(title = "咨询设置", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('base:rangeConfig:addConsult')")
    @PostMapping("/addConsult")
    @RepeatSubmit
    public AjaxResult addConsult(@Validated @RequestBody List<RangeConfig> rangeConfig) {
        for (RangeConfig rangeConfig1 : rangeConfig) {
            rangeConfig1.setTypeGroup(RangeConfigTypeConstant.CONSULT_SETTING);
            rangeConfig1.setCreateBy(getUsername());
        }
        return toAjax(rangeConfigService.insertRangeConfig(rangeConfig));
    }

    @ApiOperation(value = "修改咨询设置")
    @Log(title = "咨询设置", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('base:rangeConfig:editConsult')")
    @PutMapping("/editConsult")
    @RepeatSubmit
    public AjaxResult editConsult(@Validated @RequestBody List<RangeConfig> rangeConfig) {
        for (RangeConfig rangeConfig1 : rangeConfig) {
            rangeConfig1.setUpdateBy(getUsername());
        }
        return toAjax(rangeConfigService.updateRangeConfig(rangeConfig));
    }

    @ApiOperation(value = "分页查询咨询设置")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", name = "pid", value = "父类ID", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "类型", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "name", value = "类型", dataType = "String")
    })
    @DynamicResponseParameters(name = "RangeConfig_listConsult", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = RangeConfig[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listConsult")
    public TableDataInfo listConsult(@RequestParam Map<String, Object> map) {
        startPage();
        map.put("typeGroup", RangeConfigTypeConstant.CONSULT_SETTING);
        List<RangeConfig> list = rangeConfigService.selectRangeConfigList(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "id查询咨询设置")
    @DynamicResponseParameters(name = "RangeConfig_getInfoConsult", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = RangeConfig.class)})
    @PreAuthorize("@ss.hasPermi('base:config:getInfoConsult')")
    @GetMapping("/getInfoConsult")
    public AjaxResult getInfoConsult(Long id) {
        return AjaxResult.success(rangeConfigService.getRangeConfigById(id));
    }


    @ApiOperation(value = "删除咨询设置")
    @Log(title = "咨询设置", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('base:config:removeConsult')")
    @DeleteMapping("/removeConsult/{id}")
    public AjaxResult removeConsult(@PathVariable Long[] id) {
        return id.length > 0 ? toAjax(rangeConfigService.deleteRangeConfig(id)) : AjaxResult.error();
    }
}
