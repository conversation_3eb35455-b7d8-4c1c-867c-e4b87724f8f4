package com.dg.wss.web.controller.attendance;

import cn.hutool.core.date.DateUtil;
import com.dg.wss.attendance.domain.AttendanceClock;
import com.dg.wss.attendance.domain.vo.AttendanceClockVo;
import com.dg.wss.attendance.domain.vo.DayAttendanceVo;
import com.dg.wss.attendance.service.IAttendanceClockService;
import com.dg.wss.attendance.service.IAttendanceLeaveService;
import com.dg.wss.attendance.service.IClockHistoryService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.constant.Constants;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.oa.domain.OaRequest;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Api(tags = "考勤打卡")
@RestController
@RequestMapping("/attendance/attendanceClock")
public class AttendanceClockController extends BaseController {
    @Autowired
    private IAttendanceClockService attendanceClockService;

    @Autowired
    private IAttendanceLeaveService attendanceLeaveService;

    @Autowired
    private IClockHistoryService clockHistoryService;

    @ApiOperation(value = "添加考勤打卡")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "name", "avatar", "late", "early", "lack", "trip", "clock", "avgWork", "sumOverTime"})
    @DynamicResponseParameters(name = "attendanceClock_add",
            properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/add")
    @RepeatSubmit
    @Log(title = "考勤打卡", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated @RequestBody AttendanceClockVo attendanceClock) {
        attendanceClock.setCreateBy(getUserId());
        attendanceClock.setCircleId(getCircleId());
        return attendanceClockService.addAttendanceClock(attendanceClock);
    }

    @ApiOperation(value = "添加无考勤打卡")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "name", "avatar", "late", "early", "lack", "trip", "avgWork", "sumOverTime"})
    @DynamicResponseParameters(name = "attendanceClock_add",
            properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/addForNoAttendanceClock")
    @RepeatSubmit
    @Log(title = "添加无考勤打卡", businessType = BusinessType.INSERT)
    public AjaxResult addForNoAttendanceClock(@Validated @RequestBody AttendanceClockVo attendanceClock) {
        attendanceClock.setCreateBy(getUserId());
        attendanceClock.setCircleId(getCircleId());
        return attendanceClockService.addForNoAttendanceClock(attendanceClock);
    }

    @ApiOperation(value = "考勤组添加打卡")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "name", "avatar", "late", "early", "lack", "trip", "clock", "avgWork", "sumOverTime"})
    @DynamicResponseParameters(name = "attendanceClock_createClockByGroup",
            properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/createClockByGroup")
    @RepeatSubmit
    @Log(title = "生成考勤打卡", businessType = BusinessType.INSERT)
    public AjaxResult createClockByGroup(@ApiParam("考勤组ID") @RequestParam Long groupId, @ApiParam("考勤日期") @RequestParam Date date) {
        attendanceClockService.createClockByGroup(groupId, date);
        return AjaxResult.success();
    }

    @ApiOperation(value = "编辑考勤打卡")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime", "name", "avatar", "late", "early", "lack", "trip", "clock", "avgWork", "sumOverTime"})
    @DynamicResponseParameters(name = "attendanceClock_edit",
            properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@Validated @RequestBody AttendanceClockVo attendanceClock) {
        attendanceClock.setCircleId(getCircleId());
        attendanceClock.setCreateBy(getUserId());
        return attendanceClockService.editAttendanceClock(attendanceClock);
    }

    @ApiOperation(value = "考勤组编辑打卡")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "name", "avatar", "late", "early", "lack", "trip", "clock", "avgWork", "sumOverTime"})
    @DynamicResponseParameters(name = "attendanceClock_update",
            properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/updateAttendanceClock")
    @RepeatSubmit
    @Log(title = "修改考勤打卡", businessType = BusinessType.UPDATE)
    public AjaxResult updateAttendanceClock(@RequestBody AttendanceClock attendanceClock) {
        return toAjax(attendanceClockService.updateAttendanceClock(attendanceClock));
    }

    @ApiOperation(value = "考勤组批量编辑打卡")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "name", "avatar", "late", "early", "lack", "trip", "clock", "avgWork", "sumOverTime"})
    @DynamicResponseParameters(name = "attendanceClock_batchUpdateTypeAndWork",
            properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/batchUpdateTypeAndWork")
    @RepeatSubmit
    @Log(title = "批量修改考勤打卡", businessType = BusinessType.UPDATE)
    public AjaxResult batchUpdateTypeAndWork(@RequestBody List<AttendanceClock> list) {
        return toAjax(attendanceClockService.batchUpdateTypeAndWork(list));
    }

    @ApiOperation(value = "更新晚走次日早上晚到时间")
    @DynamicResponseParameters(name = "attendanceClock_updateLater",
            properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/updateLater")
    @RepeatSubmit
    public AjaxResult updateLater(@ApiParam("用户ID") @RequestParam Long userId, @ApiParam("考勤组ID") @RequestParam Long groupId, @ApiParam("本次打卡班次ID") @RequestParam Long shiftId, @ApiParam("考勤记录ID") @RequestParam Long clockId, @ApiParam("查询日期") @RequestParam Date date) {
        AttendanceClock clock = attendanceClockService.updateLater(userId, groupId, shiftId, clockId, date);
        return clock == null ? AjaxResult.error() : AjaxResult.success(clock);
    }

    @ApiOperation(value = "根据考勤创建考勤打卡")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "name", "avatar", "late", "early", "lack", "trip", "clock", "avgWork", "sumOverTime"})
    @DynamicResponseParameters(name = "attendanceClock_createClockByUser",
            properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/createClockByUser")
    @RepeatSubmit
    @Log(title = "生成考勤打卡", businessType = BusinessType.INSERT)
    public AjaxResult createClockByUser(@ApiParam("用户ID") @RequestParam Long userId, @ApiParam("考勤组ID") @RequestParam Long groupId, @ApiParam("日期") @RequestParam Date date) {
        return attendanceClockService.createClockByUser(groupId, userId, date, true);
    }

    @ApiOperation(value = "查询当天考勤")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据", required = true, dataTypeClass = DayAttendanceVo.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/selectAttendanceByNow")
    public AjaxResult selectAttendanceByNow() {
        return AjaxResult.success(attendanceClockService.selectAttendanceByDate(new Date(), getCircleId(), getUserId(), true));
    }

    @ApiOperation(value = "查询某天考勤")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据", required = true, dataTypeClass = DayAttendanceVo.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/selectAttendanceByDate")
    public AjaxResult selectAttendanceByDate(@ApiParam("时间") @RequestParam(required = true) Date date
            , @ApiParam("指定用户") @RequestParam(required = false) Long userId) {
        if(userId == null){
            userId = getUserId();
        }
        return AjaxResult.success(attendanceClockService.selectAttendanceByDate(date, getCircleId(), userId, true));
    }

    @ApiOperation(value = "分页查询考勤")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据", required = true, dataTypeClass = AttendanceClockVo.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listVo")
    public TableDataInfo listVo(@RequestParam Map<String, Object> map) {
        map.put("circleId", getCircleId());
        startPage();
        return getDataTable(attendanceClockService.listVo(map));
    }

    @ApiOperation(value = "分页查询考勤,post方式")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据", required = true, dataTypeClass = AttendanceClockVo.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/listVoPost")
    public TableDataInfo listVoPost(@RequestParam Map<String, Object> map) {
        map.put("circleId", getCircleId());
        startPage();
        return getDataTable(attendanceClockService.listVo(map));
    }

    @ApiOperation(value = "查询最后一次考勤记录")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据", required = true, dataTypeClass = AttendanceClock.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getLastClock")
    public AjaxResult getLastClock(@ApiParam("用户ID") @RequestParam Long userId, @ApiParam("考勤组ID") @RequestParam Long groupId) {
        return AjaxResult.success(attendanceClockService.getLastClock(userId, groupId));
    }

    @ApiOperation(value = "查询用户某天的打卡记录")
    @DynamicResponseParameters(name = "attendanceClock_getClockByUserDate",
            properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
                    @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AttendanceClock[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getClockByUserDate")
    public AjaxResult getClockByUserDate(@ApiParam("用户ID") @RequestParam Long userId, @ApiParam("考勤组ID") Long groupId, @ApiParam("查询的日期") @RequestParam Date date) {
        Long circleId = DateUtil.compare(date, Constants.NO_CIRCLE_CLOCK) < 0 ? null : getCircleId();// 7月5号之前没有圈子信息
        return AjaxResult.success(attendanceClockService.getClockByUserDate(userId, circleId, groupId, date));
    }

    @ApiOperation(value = "判断是否进入考勤区域")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/checkClock")
    public AjaxResult checkClock(Long userId, Long groupId, String bssid, double latitude, double longitude) {
        return AjaxResult.success(attendanceClockService.checkClock(userId, groupId, bssid, latitude, longitude));
    }

    @ApiOperation(value = "圈子统计")
    @DynamicResponseParameters(name = "attendanceClock_circleCount",
            properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
                    @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AttendanceClockVo[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/circleCount")
    public AjaxResult circleCount(@ApiParam("考勤组ID，多个用逗号隔开") @RequestParam(required = false) String groupId, @ApiParam("开始时间") @RequestParam Date start, @ApiParam("结束时间") @RequestParam Date end) {
        return AjaxResult.success(attendanceClockService.circleCount(getUserId(), getCircleId(), groupId, start, end));
    }

    @ApiOperation(value = "考勤组统计")
    @DynamicResponseParameters(name = "attendanceClock_groupCount",
            properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
                    @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AttendanceClockVo[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/groupCount")
    public AjaxResult groupCount(@ApiParam("考勤组ID") @RequestParam(required = false) Long groupId,
                                 @ApiParam("用户ID，多个可以使用数组") @RequestParam(required = false) String userId,
                                 @ApiParam("开始时间") @RequestParam Date start,
                                 @ApiParam("结束时间") @RequestParam Date end,
                                 @ApiParam("考勤组IDList,逗号分割") @RequestParam(required = false) String groupIdList) {
        return AjaxResult.success(attendanceClockService.groupCount(getCircleId(), groupId, userId, start, end, groupIdList));
    }

    @ApiOperation(value = "查询异常考勤")
    @DynamicResponseParameters(name = "attendanceClock_selectAbnormalByUser",
            properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
                    @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AttendanceClockVo[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/selectAbnormalByUser")
    public AjaxResult selectAbnormalByUser(@ApiParam("考勤组ID") Long groupId, @ApiParam("异常类型") @RequestParam String type, @ApiParam("用户ID") @RequestParam Long userId, @ApiParam("开始时间") @RequestParam Date start, @ApiParam("结束时间") @RequestParam Date end) {
        return AjaxResult.success(attendanceClockService.selectAbnormalByUser(getCircleId(), userId, groupId, type, start, end));
    }

    @ApiOperation(value = "报表，导出Excel")
    @DynamicResponseParameters(name = "attendanceClock_selectCountByDate",
            properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
                    @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AttendanceClockVo[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/selectCountByDate")
    public AjaxResult selectCountByDate(@ApiParam("考勤组ID") @RequestParam(required = false) Long groupId, @ApiParam("用户ID，如果考勤组ID为空，用户ID只能为本，多个用,拼接") @RequestParam(required = false) String userId, @ApiParam("开始时间") @RequestParam Date start, @ApiParam("结束时间") @RequestParam Date end,
                                        @ApiParam("1和true默认模板，2新模板") @RequestParam String exportExcel) {
        // if (groupId == null) userId = getUserId();
        return AjaxResult.success(attendanceClockService.selectCountByDate(getCircleId(), groupId, getUserId(), userId, start, end, exportExcel));
    }

    @ApiOperation(value = "删除考勤打卡")
    @DynamicResponseParameters(name = "attendanceClock_delete",
            properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @DeleteMapping("/deleteById/{id}")
    @RepeatSubmit
    public AjaxResult groupCount(@PathVariable Long id) {
        return toAjax(attendanceClockService.removeById(id));
    }

    @ApiOperation(value = "获取某天考勤打卡记录")
    @DynamicResponseParameters(name = "attendanceClock_getAllClockByUser",
            properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @DeleteMapping("/getAllClockByUser")
    @RepeatSubmit
    public AjaxResult getAllClockByUser(@ApiParam("考勤组ID") @RequestParam() Long groupId, @ApiParam("用户ID") @RequestParam() Long userId, @ApiParam("打卡日") @RequestParam() Date day) {
        return AjaxResult.success(clockHistoryService.getAllClockByUser(getCircleId(), groupId, userId, day));
    }

    @ApiOperation(value = "打卡时间报表导出")
    @DynamicResponseParameters(name = "request_export", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = OaRequest.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/exportReport")
    public AjaxResult exportReport(@ApiParam("圈ID") @RequestParam(required = false) Long circleId,@ApiParam("考勤组ID") @RequestParam(required = false) Long groupId, @ApiParam("用户ID，多个用,拼接") @RequestParam(required = false) Long userId,
                                   @ApiParam("开始时间") @RequestParam(required = false) Date start, @ApiParam("结束时间") @RequestParam(required = false) Date end) {
        return attendanceClockService.exportReport(circleId,getCircleId(), groupId, userId, start, end);
    }

    @ApiOperation(value = "设备打卡数据导出")
    @DynamicResponseParameters(name = "request_export", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = OaRequest.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/exportReportForDevice")
    public AjaxResult exportReportForDevice(@ApiParam("圈ID") @RequestParam(required = false) Long circleId,@ApiParam("考勤组ID") @RequestParam(required = false) Long groupId, @ApiParam("用户ID，多个用,拼接") @RequestParam(required = false) Long userId,
                                   @ApiParam("开始时间") @RequestParam(required = false) Date start, @ApiParam("结束时间") @RequestParam(required = false) Date end) throws ParseException {
        return attendanceClockService.exportReportForDevice(circleId,getCircleId(), groupId, userId, start, end);
    }

    @ApiOperation(value = "获取审批时长")
    @DynamicResponseParameters(name = "attendanceClock_getRequestMinute", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = OaRequest.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getRequestMinute")
    public AjaxResult getRequestMinute(@ApiParam("考勤组ID") Long groupId, @ApiParam("用户ID") @RequestParam(required = false) Long userId
            , @ApiParam("开始时间") Date start, @ApiParam("结束时间") Date end, @ApiParam("请假规则id") Long ruleId) {
        return AjaxResult.success(attendanceLeaveService.getLeaveRequestMinute(this.getCircleId(), groupId, userId, start, end, ruleId));
    }

    @ApiOperation(value = "更新指定考勤日的打卡记录，根据排班，请假和加班等申请，以及历史打卡记录重新生成考勤缺卡记录并匹配打卡")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class), @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据", required = true, dataTypeClass = DayAttendanceVo.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/refreshAttendanceClockByDate")
    public AjaxResult refreshAttendanceClockByDate(@ApiParam("考勤日") @RequestParam(required = true) Date date
            , @ApiParam("指定用户") @RequestParam(required = true) Long userId
            , @ApiParam("考勤组ID") @RequestParam(required = false) Long groupId) {
        attendanceClockService.refreshAttendanceClockByDate(getCircleId(), userId, date, groupId);

        return AjaxResult.success(attendanceClockService.selectAttendanceByDate(date, getCircleId(), userId, true));
    }

    @ApiOperation(value = "获取审批时长-按天")
    @DynamicResponseParameters(name = "attendanceClock_getRequestMinute", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = OaRequest.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getRequestDay")
    public AjaxResult getRequestDay(@ApiParam("考勤组ID") Long groupId, @ApiParam("用户ID") @RequestParam(required = false) Long userId, @ApiParam("开始时间") Date start, @ApiParam("结束时间") Date end) {
        return AjaxResult.success(attendanceLeaveService.getRequestDay(groupId, userId, start, end));
    }
}
