package com.dg.wss.web.controller.anti;

import com.dg.wss.anti.domain.AntiDeviceStation;
import com.dg.wss.anti.service.IAntiDeviceStationService;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.commonservice.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * create by chenjunsong
 * Date 2024/5/28 16:13
 * Description
 */

@Api(tags = "防错料")
@RestController
@RequestMapping("/anti/deviceStation")
public class AntiDeviceStationController extends BaseController {

    @Resource
    private IAntiDeviceStationService antiDeviceStationService;

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> map) {
        startPage();
        TableDataInfo tableDataInfo = getDataTable(antiDeviceStationService.list(map));
        return tableDataInfo;
    }


    @ApiOperation(value = "添加")
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody AntiDeviceStation antiDeviceStation) {

        antiDeviceStation.setCircleId(getCircleId());
        return new AjaxResult(200,"操作成功",antiDeviceStationService.add(antiDeviceStation));
    }

    @ApiOperation(value = "添加-批量")
    @RepeatSubmit
    @PostMapping("/addBatch")
    public AjaxResult addBatch(@Validated @RequestBody AntiDeviceStation antiDeviceStation) {

        return new AjaxResult(200,"操作成功",antiDeviceStationService.addBatch(antiDeviceStation));
    }

    @ApiOperation(value = "编辑")
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody AntiDeviceStation antiDeviceStation) {
        return toAjax(antiDeviceStationService.edit(antiDeviceStation));
    }

    @ApiOperation(value = "删除")
    @RepeatSubmit
    @DeleteMapping("/delete/{id}")
    public AjaxResult delete(@PathVariable("id") Long id) {

        return toAjax(antiDeviceStationService.delete(id));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail")
    public AjaxResult detail(@RequestParam Long id) {
        return new AjaxResult(200,"操作成功",antiDeviceStationService.detail(id));
    }




}
