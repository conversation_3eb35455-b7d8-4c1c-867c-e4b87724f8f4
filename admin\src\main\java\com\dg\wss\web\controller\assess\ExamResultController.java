package com.dg.wss.web.controller.assess;


import com.dg.wss.assess.domain.ExamResult;
import com.dg.wss.assess.domain.vo.ExamResultVo;
import com.dg.wss.assess.service.IExamResultService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import com.lowagie.text.DocumentException;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Api(tags = "学习任务-结果")
@RestController
@RequestMapping("/assess/examResult")
public class ExamResultController extends BaseController {

    @Autowired
    private IExamResultService examResultService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "roleId", value = "角色id", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "examId", value = "科目id", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "1通过 -1考试不通过 0未开始 2继续学习", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "createBy", value = "用户id", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "datetime"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "datetime")
    })
    @DynamicResponseParameters(name = "ExamResult_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = ExamResult[].class)
    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<ExamResult> list = examResultService.selectByMap(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "分页查询-如果没有结果，则自动创建记录")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "roleId", value = "角色id", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "examId", value = "科目id", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "1通过 -1考试不通过 0未开始 2继续学习", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "createBy", value = "用户id", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "datetime"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "datetime")
    })
    @DynamicResponseParameters(name = "ExamResult_listDefault", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = ExamResult[].class)
    })
    @GetMapping("/listDefault")
    public TableDataInfo listDefault(Long roleId) {
        startPage();
        if (roleId == null) {
            List<ExamResultVo> list = examResultService.listDefault(getUserId());
            return getDataTable(list);
        }
        List<ExamResultVo> list = examResultService.listDefault(roleId, getUserId());
        return getDataTable(list);
    }

    @ApiOperation(value = "学习情况页面，根据examId 查询所有人的考试结果")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "examId", value = "必传,科目id", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "userName", value = "用户姓名", dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "phone", value = "手机号", dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "roleId", value = "角色id", dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "状态，1通过，-1未通过，0未开始，2继续学习", dataType = "datetime")
    })
    @DynamicResponseParameters(name = "ExamResult_listByExam", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = ExamResultVo[].class)
    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listByExam")
    public TableDataInfo listByExam(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<ExamResultVo> list = examResultService.selectByExam(map);
        return getDataTable(list);
    }


    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "ExamResult_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "学习任务-结果", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasRole('basics')")
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@RequestBody ExamResult examResult) {
        examResult.setCreateBy(getUserId());
        examResult.setCreateTime(new Date());
        return toAjax(examResultService.insert(examResult));
    }


    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "ExamResult_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "学习任务-结果", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody ExamResult examResult) {
        examResult.setUpdateBy(getUserId());
        return toAjax(examResultService.update(examResult));
    }


    @ApiOperation(value = "结束学习任务")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", value = "必传,学习结果id，examResultId", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "examId", value = "必传,科目id", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "qa", value = "问题id:答案数组，例如{\"1234124\":\"1,3\",\"1423324\":\"2,3\"}", dataType = "string")
    })
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "ExamResult_submit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "学习任务-结果", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/submit")
    public AjaxResult submit(@ApiIgnore @RequestBody ExamResult examResult) throws SQLException {
        examResult.setUpdateBy(getUserId());
        return toAjax(examResultService.submit(examResult));
    }


    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(name = "examResult_getInfo", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ExamResultVo.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(examResultService.selectVoById(id));
    }


    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "ExamResult_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "学习任务-结果", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(examResultService.delete(id));
    }

    @ApiOperation(value = "获取证书的pdf和图片下载链接")
    @DynamicResponseParameters(name = "examResult_getInfo", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ExamResultVo.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getCertificate")
    public AjaxResult getCertificate(@ApiParam("学习结果ID") @RequestParam Long examResultId) throws DocumentException, IOException {
        return (examResultService.getCertificate(getCircleId(), getUserId(), examResultId));
    }

    @ApiOperation(value = "统计证书数量")
    @DynamicResponseParameters(name = "examResult_getInfo", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ExamResultVo.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/certificateCount")
    public AjaxResult certificateCount() {
        return (examResultService.certificateCount(getUserId()));
    }

}
