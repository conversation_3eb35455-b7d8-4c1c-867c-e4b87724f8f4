package com.dg.wss.web.controller.all;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;

import com.dg.wss.all.domain.AllAlipayConfig;
import com.dg.wss.all.service.IAllAlipayConfigService;

import java.util.Date;
import java.util.List;

import org.springframework.web.bind.annotation.RestController;
import com.dg.wss.commonservice.BaseController;

/**
 * <p>
 * 圈子支付宝配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@RestController
@RequestMapping("/all/AlipayConfig")
public class AllAlipayConfigController extends BaseController {
    @Autowired
    private IAllAlipayConfigService allAlipayConfigService;


    /**
     * 查询圈子支付宝配置表列表
     */
    @ApiOperation(value = "查询圈子支付宝配置表列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo list(@ModelAttribute AllAlipayConfig allAlipayConfig) {
        fillTime(allAlipayConfig);
        startPage();
        allAlipayConfig.setCircleId(getCircleId());
        List<AllAlipayConfig> list = allAlipayConfigService.listAllAlipayConfig(allAlipayConfig);
        return getDataTable(list);
    }

    /**
     * 查询圈子支付宝配置表详情
     */
    @ApiOperation(value = "查询圈子支付宝配置表详情")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult list(@RequestParam Long id) {
        return AjaxResult.success(allAlipayConfigService.getById(id));
    }

    /**
    * 新增圈子支付宝配置表信息
    */
    @ApiOperation(value = "新增圈子支付宝配置表信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "圈子支付宝配置表", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@RequestBody AllAlipayConfig allAlipayConfig){
        allAlipayConfig.setCreateBy(getUserId());
        allAlipayConfig.setCircleId(getCircleId());
        allAlipayConfig.setCreateTime(new Date());
        return AjaxResult.success(allAlipayConfigService.save(allAlipayConfig));
    }

    /**
    * 批量修改保存scm检验项目
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "scm检验项目", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAdd")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchAdd(@RequestBody List<AllAlipayConfig> allAlipayConfigs) {
        for (AllAlipayConfig allAlipayConfig : allAlipayConfigs) {
            allAlipayConfig.setCircleId(getCircleId());
            allAlipayConfig.setCreateBy(getUserId());
            allAlipayConfig.setCreateTime(new Date());
        }
        return AjaxResult.success(allAlipayConfigService.saveBatch(allAlipayConfigs));
    }

    /**
     * 修改圈子支付宝配置表信息
     */
    @ApiOperation(value = "修改圈子支付宝配置表信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "圈子支付宝配置表", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@RequestBody AllAlipayConfig allAlipayConfig){
        allAlipayConfig.setUpdateBy(getUserId());
        allAlipayConfig.setCircleId(getCircleId());
        allAlipayConfig.setUpdateTime(new Date());
        return AjaxResult.success(allAlipayConfigService.updateById(allAlipayConfig));
    }

    /**
    * 批量修改圈子支付宝配置表
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "圈子支付宝配置表", businessType = BusinessType.UPDATE)
    @PutMapping("/batchEdit")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchEdit(@RequestBody List<AllAlipayConfig> allAlipayConfigs) {
        for (AllAlipayConfig allAlipayConfig : allAlipayConfigs) {
            allAlipayConfig.setCircleId(getCircleId());
            allAlipayConfig.setUpdateBy(getUserId());
            allAlipayConfig.setUpdateTime(new Date());
        }
        return AjaxResult.success(allAlipayConfigService.updateBatchById(allAlipayConfigs));
    }

    /**
     * 删除圈子支付宝配置表信息
     */
    @ApiOperation(value = "删除圈子支付宝配置表信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "圈子支付宝配置表", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable String id){
        return AjaxResult.success(allAlipayConfigService.removeById(id));
    }

    @ApiOperation(value = "查询支付宝余额")
    @GetMapping("/balanceQuery")
    public AjaxResult balanceQuery() {
        return AjaxResult.success(allAlipayConfigService.getBalance(getCircleId()));
    }
}
