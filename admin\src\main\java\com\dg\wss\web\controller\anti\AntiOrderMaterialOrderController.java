package com.dg.wss.web.controller.anti;

import com.dg.wss.anti.domain.AntiOrderMaterialOrder;
import com.dg.wss.anti.service.IAntiMaterialOrderService;
import com.dg.wss.anti.service.IAntiOrderMaterialOrderService;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.commonservice.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * create by chenjunsong
 * Date 2024/5/28 16:13
 * Description
 */

@Api(tags = "防错料-工单关联料单")
@RestController
@RequestMapping("/anti/orderMaterialOrder")
public class AntiOrderMaterialOrderController extends BaseController {

    @Resource
    private IAntiOrderMaterialOrderService antiOrderMaterialOrderService;

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> map) {
        startPage();
        TableDataInfo tableDataInfo = getDataTable(antiOrderMaterialOrderService.list(map));
        return tableDataInfo;
    }


    @ApiOperation(value = "添加")
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody AntiOrderMaterialOrder antiOrderMaterialOrder) {
        antiOrderMaterialOrder.setCreateBy(getUserId());
        antiOrderMaterialOrder.setCircleId(getCircleId());
        return new AjaxResult(200,"操作成功",antiOrderMaterialOrderService.add(antiOrderMaterialOrder));
    }

    @ApiOperation(value = "编辑")
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody AntiOrderMaterialOrder antiOrderMaterialOrder) {
        antiOrderMaterialOrder.setUpdateBy(getUserId());
        return toAjax(antiOrderMaterialOrderService.edit(antiOrderMaterialOrder));
    }

    @ApiOperation(value = "删除")
    @RepeatSubmit
    @DeleteMapping("/delete/{id}")
    public AjaxResult delete(@PathVariable("id") Long id) {

        return toAjax(antiOrderMaterialOrderService.delete(id));
    }







}
