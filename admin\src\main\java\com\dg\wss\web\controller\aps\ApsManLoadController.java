package com.dg.wss.web.controller.aps;

import com.dg.wss.aps.domain.ApsWorkType;
import com.dg.wss.aps.service.IApsManLoadService;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.scm.domain.mm.ScmWorkType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "aps人荷")
@RestController
@RequestMapping("/aps/apsManLoad")
public class ApsManLoadController extends BaseController {
    @Autowired
    private IApsManLoadService manLoadService;

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody ScmWorkType scmWorkType) {
        startPage();
        scmWorkType.setCircleId(getCircleId());
        List<ApsWorkType> list = manLoadService.pageByMap(scmWorkType);
        return getDataTable(list);
    }

    @ApiOperation(value = "列出全部人员，可以根据工种过滤")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listAllMan")
    public TableDataInfo listAllMan(@RequestParam Map<String,Object> map ) {
        startPage();
        map.put("circleId",getCircleId());
        return getDataTable(manLoadService.listAllMan(map));
    }

}