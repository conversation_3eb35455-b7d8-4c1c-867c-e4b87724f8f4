package com.dg.wss.web.controller.aps;

import com.dg.wss.aps.domain.ApsWorkOrder;
import com.dg.wss.aps.service.IApsWorkOrderService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Api(tags = "aps令单")
@RestController
@RequestMapping("/aps/apsWorkOrder")
public class ApsWorkOrderController extends BaseController {
    @Autowired
    private IApsWorkOrderService apsWorkOrderService;

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<ApsWorkOrder> list = apsWorkOrderService.pageByMap(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps令单", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ApsWorkOrder apsWorkOrder) {
        apsWorkOrder.setCreateBy(getUserId());
        apsWorkOrder.setCreateTime(new Date());
        return toAjax(apsWorkOrderService.insert(apsWorkOrder));
    }

    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps令单", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody ApsWorkOrder apsWorkOrder) {
        apsWorkOrder.setUpdateBy(getUserId());
        apsWorkOrder.setUpdateTime(new Date());
        return toAjax(apsWorkOrderService.update(apsWorkOrder));
    }

    @ApiOperation(value = "查询by id")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(apsWorkOrderService.selectById(id));
    }

    @ApiOperation(value = "删除")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps令单", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(apsWorkOrderService.delete(id));
    }
}