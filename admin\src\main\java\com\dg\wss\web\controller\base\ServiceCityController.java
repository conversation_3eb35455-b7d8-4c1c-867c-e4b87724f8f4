package com.dg.wss.web.controller.base;

import com.dg.wss.base.domain.ServiceCity;
import com.dg.wss.base.service.IServiceCityService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

@Api(tags = "运营城市")
@RestController
@RequestMapping("/base/serviceCity")
public class ServiceCityController extends BaseController {

    @Autowired
    private IServiceCityService serviceCityService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "大小", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "页号", dataType = "String")})
    @DynamicResponseParameters(name = "serviceCity_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = ServiceCity[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<ServiceCity> list = serviceCityService.selectServiceCityList(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "id查询运营城市")
    @DynamicResponseParameters(name = "serviceCity_getInfo", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ServiceCity.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("充值订单ID") @RequestParam Long id) {
        return AjaxResult.success(serviceCityService.selectServiceCityById(id));
    }

    @ApiOperation(value = "修改运营城市")
    @ApiOperationSupport(ignoreParameters = {"country", "province", "city", "cityCode", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "serviceCity_edit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),})
    @Log(title = "运营城市", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('financial:serviceCity:edit')")
    @PutMapping("/edit")
    public AjaxResult updateStatus(@Validated @RequestBody ServiceCity serviceCity) {
        serviceCity.setUpdateBy(getUsername());
        return toAjax(serviceCityService.updateServiceCity(serviceCity));
    }

    @ApiOperation(value = "添加运营城市")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "serviceCity_add", properties = {
            @DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "运营城市", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ServiceCity serviceCity) {
        serviceCity.setCreateBy(getUsername());
        return toAjax(serviceCityService.addServiceCity(serviceCity));
    }
}
