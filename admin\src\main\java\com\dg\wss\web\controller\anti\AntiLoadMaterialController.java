package com.dg.wss.web.controller.anti;

import cn.hutool.core.util.ObjectUtil;
import com.dg.wss.anti.domain.AntiMaterialOrder;
import com.dg.wss.anti.service.IAntiMaterialOrderService;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.commonservice.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * create by chenjunsong
 * Date 2024/5/28 16:13
 * Description
 */

@Api(tags = "防错料-上料")
@RestController
@RequestMapping("/anti/loadMaterial")
public class AntiLoadMaterialController extends BaseController {

    @Resource
    private IAntiMaterialOrderService antiMaterialOrderService;

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> map) {
        startPage();
        TableDataInfo tableDataInfo = getDataTable(antiMaterialOrderService.list(map));
        return tableDataInfo;
    }


    @ApiOperation(value = "添加")
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody AntiMaterialOrder antiMaterialOrder) {
        antiMaterialOrder.setCreateBy(getUserId());
        antiMaterialOrder.setCircleId(getCircleId());
        return new AjaxResult(200,"操作成功",antiMaterialOrderService.add(antiMaterialOrder));
    }

    @ApiOperation(value = "编辑")
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody AntiMaterialOrder antiMaterialOrder) {
        antiMaterialOrder.setUpdateBy(getUserId());
        return toAjax(antiMaterialOrderService.edit(antiMaterialOrder));
    }

    @ApiOperation(value = "删除")
    @RepeatSubmit
    @DeleteMapping("/delete/{id}")
    public AjaxResult delete(@PathVariable("id") Long id) {

        return toAjax(antiMaterialOrderService.delete(id));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail")
    public AjaxResult detail(@RequestParam Long id) {
        return new AjaxResult(200,"操作成功",antiMaterialOrderService.detail(id));
    }

    @ApiOperation(value = "根据设备id查工单列表")
    @GetMapping("/getOrderListByDeviceId")
    public TableDataInfo getOrderListByDeviceId(@RequestParam(required = false)  Long deviceId) {
        startPage();
        TableDataInfo tableDataInfo = getDataTable(antiMaterialOrderService.getOrderListByDeviceId(deviceId,getCircleId()));
        return tableDataInfo;
    }

    @ApiOperation(value = "根据设备id列表查工单列表")
    @GetMapping("/getOrderListByDeviceIds")
    public TableDataInfo getOrderListByDeviceIds(@RequestParam List<Long>  deviceIds) {
        startPage();
        TableDataInfo tableDataInfo = getDataTable(antiMaterialOrderService.getOrderListByDeviceIds(deviceIds,getCircleId()));
        return tableDataInfo;
    }


    @ApiOperation(value = "根据设备id查工单列表")
    @GetMapping("/getOrderAntiHistory")
    public TableDataInfo getOrderAntiHistory(@RequestParam Map<String,Object> map) {
        startPage();
        if (ObjectUtil.isEmpty(map.get("circleId"))) map.put("circleId",getCircleId());
        TableDataInfo tableDataInfo = getDataTable(antiMaterialOrderService.getOrderAntiHistory(map));
        return tableDataInfo;
    }








}
