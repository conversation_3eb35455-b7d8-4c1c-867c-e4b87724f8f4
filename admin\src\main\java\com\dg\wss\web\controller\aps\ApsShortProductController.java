package com.dg.wss.web.controller.aps;

import com.dg.wss.aps.domain.ApsShortProductVo;
import com.dg.wss.aps.service.IApsShortProductService;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.commonservice.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "aps短料")
@RestController
@RequestMapping("/aps/apsShortProduct")
public class ApsShortProductController extends BaseController {
    @Autowired
    private IApsShortProductService apsShortProductService;

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ModelAttribute ApsShortProductVo apsShortProductVo) {
        startPage();
        apsShortProductVo.setCircleId(getCircleId());
        List<ApsShortProductVo> list = apsShortProductService.selectShortProduct(apsShortProductVo);
        return getDataTable(list);
    }

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listByProduct")
    public TableDataInfo listByProduct(@ModelAttribute ApsShortProductVo apsShortProductVo) {
        startPage();
        apsShortProductVo.setCircleId(getCircleId());
        List<ApsShortProductVo> list = apsShortProductService.listByShortProduct(apsShortProductVo);
        return getDataTable(list);
    }
}