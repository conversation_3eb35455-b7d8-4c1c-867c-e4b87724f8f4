package com.dg.wss.web.controller.acl;

import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.common.utils.StringUtils;
import com.dg.wss.commonservice.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestBody;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.acl.domain.AclBlackwhiteList;
import com.dg.wss.acl.service.IAclBlackwhiteListService;
import com.dg.wss.common.core.domain.AjaxResult;

/**
 * 黑白名单控制列表Controller
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Controller
@Api(tags = "黑白名单控制列表")
@RequestMapping("/acl/aclblackwhitelist")
public class AclBlackwhiteListController extends BaseController
{
    @Autowired
    private IAclBlackwhiteListService aclBlackwhiteListService;

    /**
     * 查询圈子下所有应用模块的白名单控制列表
     */
    @ApiOperation(value = "根据圈子id，模块类型，查询某个模块的黑名单控制列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/aclControlBlackList")
    @ResponseBody
    public AjaxResult aclControlBlackList(@RequestParam(required = true) Long circleId)
    {
        Map<String,List<AclBlackwhiteList>> map = aclBlackwhiteListService.selectAclBlackListList(circleId);
        return AjaxResult.success(map);
    }

    /**
     * 查询黑名单控制列表列表
     */
    @ApiOperation(value = "根据圈子id，模块类型，查询某个模块的黑名单控制列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/aclSettingBlackList")
    @ResponseBody
    public AjaxResult aclSettingBlackList(@RequestParam(required = true) Long circleId, @RequestParam(required = true) String appType)
    {
        List<AclBlackwhiteList> list = aclBlackwhiteListService.selectAclBlackListList(circleId, appType);
        return AjaxResult.success(list);
    }

    /**
     * 查询圈子下所有应用模块的白名单控制列表
     */
    @ApiOperation(value = "根据圈子id，模块类型，查询某个模块的白名单控制列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/aclControlWhiteList")
    @ResponseBody
    public AjaxResult aclControlWhiteList(@RequestParam(required = true) Long circleId)
    {
        Map<String,List<AclBlackwhiteList>> map = aclBlackwhiteListService.selectAclWhiteListList(circleId);
        return AjaxResult.success(map);
    }

    /**
     * 查询白名单控制列表列表
     */
    @ApiOperation(value = "根据圈子id，模块类型，查询某个模块的白名单控制列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/aclSettingWhiteList")
    @ResponseBody
    public AjaxResult aclSettingWhiteList(@RequestParam(required = true) Long circleId, @RequestParam(required = true) String appType)
    {
        List<AclBlackwhiteList> list = aclBlackwhiteListService.selectAclWhiteListList(circleId, appType);
        return AjaxResult.success(list);
    }

    /**
     * 批量新增保存黑白名单控制列表
     */
    @ApiOperation(value = "批量新增保存黑白名单控制列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "黑白名单控制列表", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    @ResponseBody
    public AjaxResult batchAddSave(@RequestBody List<AclBlackwhiteList> aclBlackwhiteList)
    {
        return toAjax(aclBlackwhiteListService.batchInsertAclBlackwhiteList(aclBlackwhiteList));
    }

    /**
     * 新增保存黑白名单控制列表
     */
    @ApiOperation(value = "新增保存黑白名单控制列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "黑白名单控制列表", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody AclBlackwhiteList aclBlackwhiteList)
    {
        return toAjax(aclBlackwhiteListService.insertAclBlackwhiteList(aclBlackwhiteList));
    }

    /**
     * 删除黑白名单控制列表
     */
    @ApiOperation(value = "删除黑白名单控制列表,ids,以逗号分割")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "黑白名单控制列表", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(@RequestParam(value = "ids", required = true)  String ids)
    {
        return toAjax(aclBlackwhiteListService.deleteAclBlackwhiteListByIds(ids));
    }
}
