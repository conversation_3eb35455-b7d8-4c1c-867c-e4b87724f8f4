package com.dg.wss.web.controller.aps;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dg.wss.aps.domain.ApsCalendarSlot;
import com.dg.wss.aps.domain.ApsLot;
import com.dg.wss.aps.mapper.ApsLotMapper;
import com.dg.wss.aps.service.*;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.scm.domain.ScmBom;
import com.dg.wss.scm.domain.vo.ScmProcessVo;
import com.dg.wss.scm.mapper.ScmBomMapper;
import com.dg.wss.scm.service.IScmServiceService;
import io.swagger.annotations.Api;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "aps预警")
@RestController
@RequestMapping("/test")
public class ApsTestController extends BaseController {
    @Autowired
    @Lazy
    private ITimeFoldService timeFoldService;
    @Autowired
    private ApsLotMapper apsLotMapper;
    @Autowired
    private IApsCalendarSlotService apsCalendarSlotService;
    @Autowired
    private ScmBomMapper scmBomMapper;
    @Autowired
    private IApsTaskService apsTaskService;
    @Autowired
    private IApsLotService apsLotService;

    @Autowired
    private IScmServiceService scmServiceService;
    @Autowired
    private IApsProductLotProcessService apsProductLotProcessService;
    @GetMapping("/test")
    public String test(@RequestParam Map<String,Object> map) {
        return testMethod(map.get("ids").toString(), Integer.parseInt(map.get("duration").toString()));
    }

    @GetMapping("/testBo")
    public String testBo(@RequestParam Map<String,Object> map) {
        return testBatchOrder(map.get("ids").toString(), Integer.parseInt(map.get("duration").toString()));
    }

    @GetMapping("/getResourceOfflineTime")
    public AjaxResult getResourceOfflineTime( @ModelAttribute ApsCalendarSlot a) {
        return AjaxResult.success(apsCalendarSlotService.getResourceOfflineTime(a.getIds(),10));
    }

    @GetMapping("/allTest")
    public String allTest(@RequestParam Map<String,Object> map) {
        if("1".equals(map.get("type"))){
            getTreeStructure(Long.valueOf(map.get("id").toString()));
        }else if("2".equals(map.get("type"))){
            Long id = Long.valueOf(map.get("id").toString());
            apsTaskService.createTask(Collections.singletonList(id));
        }else if("3".equals(map.get("type"))){
            Long id = Long.valueOf(map.get("id").toString());
            apsTaskService.createTask_old(Collections.singletonList(id));
        }else if("4".equals(map.get("type"))){
            Long id = Long.valueOf(map.get("id").toString());
            ApsLot apsLot = apsLotService.selectById(id);
            apsLotService.checkMachineTimeConfig(apsLot);
        }else if("5".equals(map.get("type"))){
            //测试单个lot编码
            String lotCode = apsLotService.getLotCode();
            System.out.println(lotCode);
            return lotCode;
        }else if("6".equals(map.get("type"))){
            // 测试多个lot编码
            StringJoiner joiner = new StringJoiner(",");
            Long circleId = getCircleId();
            System.out.println(circleId);
            if(map.containsKey("key")) {
                String key = map.get("key").toString();
                List<String> strings = scmServiceService.genCodeByTable(key, "T", 3);
                String lotCode = scmServiceService.genCodeByTable(key, "LOT");
                for (String string : strings) {
                    joiner.add(string);
                }
                joiner.add(lotCode);
            }
            return circleId+":"+joiner.toString();
        }else if("7".equals(map.get("type"))){
            return "测试部署";
        }else if("8".equals(map.get("type"))){
            apsTaskService.repairData();
            return "2024年12月24日";
        }else if("9".equals(map.get("type"))){
            ScmProcessVo scmProcess = new ScmProcessVo();
            scmProcess.setId(Long.valueOf(map.get("id").toString()));
            scmProcess.setMachineTime(new BigDecimal(map.get("machineTime").toString()));
            apsProductLotProcessService.coverFromProcessTime(scmProcess);
            return "2024年12月24日";
        }
        return "ss";
    }

    // 根据job，重新生成排程结果
    @GetMapping("/generateTaskResourceByJobId")
    public String generateTaskResourceByJobId(@RequestParam Map<String,Object> map) {
        timeFoldService.generateTaskResourceByJobId(map.get("id").toString());
        return null;
    }

    private String testMethod(String ids, Integer duration) {
        List<Long> lotIds = new ArrayList<>();
//        lotIds.add(1786423611812499456L);
//        lotIds.add(1786592393461780480L);
        for (String s : ids.split(",\\s*")) {
            lotIds.add(Long.valueOf(s));
        }
        return timeFoldService.submit(lotIds, duration);
    }

    private String testBatchOrder(String boIds, Integer duration) {
        List<Long> boIdList = new ArrayList<>();
//        lotIds.add(1786423611812499456L);
//        lotIds.add(1786592393461780480L);
        for (String s : boIds.split(",")) {
            boIdList.add(Long.valueOf(s));
        }
        List<ApsLot> lotList = apsLotMapper.selectList(new LambdaQueryWrapper<ApsLot>().in(ApsLot::getBatchOrderId, boIdList));
        List<Long> lotIds = lotList.stream().map(ApsLot::getId).collect(Collectors.toList());
        System.out.println("lot数量="+lotIds.size());
        return timeFoldService.submit(lotIds, duration);
    }


    public Node getTreeStructure(Long rootId) {
        List<Node> allNodes = getAllNodes(rootId);  // 查询所有节点
        Node node = buildTree(rootId, allNodes);
        printTree(node, "-");
        return  node;// 构建树形结构
    }


    public List<Node> getAllNodes(Long productId) {
        // 假设使用MyBatis
        List<ScmBom> bomList = scmBomMapper.cteTreeInfo(productId,null);
        Node rootNode = new Node(productId, null, "根节点");
        List<Node> nodes = new ArrayList<>();
        nodes.add(rootNode);
        for (ScmBom bom : bomList) {
            Node node = new Node(bom.getSonId(), bom.getParentId(),bom.getSonName() );
            nodes.add(node);
        }
        return nodes;
    }


    @Data
    public class Node {
        private Long id;
        private Long pid;
        private String name;
        private List<Node> children;
        // Constructor, getters, and setters
        public Node(Long id, Long pid, String name) {
            this.id = id;
            this.pid = pid;
            this.name = name;
            this.children = new ArrayList<>();
        }
    }

    public Node buildTree(Long rootId, List<Node> allNodes) {
        Node root = null;
        Map<Long, Node> nodeMap = new HashMap<>();

        // 将所有节点放入Map中，方便通过id获取
        for (Node node : allNodes) {
            nodeMap.put(node.getId(), node);
        }

        // 构建树形结构
        for (Node node : allNodes) {
            if (node.getId().equals(rootId)) {
                root = node;  // 找到根节点
            }
            Node parent = nodeMap.get(node.getPid());
            if (parent != null) {
                parent.getChildren().add(node);  // 将子节点添加到父节点的children列表中
            }
        }

        return root;
    }

    public void printTree(Node node, String indent) {
        if (node == null) return;

        System.out.println(indent + node.getName());
        for (Node child : node.getChildren()) {
            printTree(child, indent + "  ");
        }
    }




}
