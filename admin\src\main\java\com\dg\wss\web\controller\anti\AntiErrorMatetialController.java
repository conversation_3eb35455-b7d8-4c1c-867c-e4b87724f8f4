package com.dg.wss.web.controller.anti;

import cn.hutool.core.util.ObjectUtil;
import com.dg.wss.anti.domain.AntiDeviceOrder;
import com.dg.wss.anti.domain.AntiErrorMaterial;
import com.dg.wss.anti.mapper.AntiDeviceOrderMapper;
import com.dg.wss.anti.service.IAntiErrorMaterialService;
import com.dg.wss.anti.service.IAntiMaterialOrderService;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.wms.domain.WmsLocation;
import com.dg.wss.wms.domain.WmsShelf;
import com.dg.wss.wms.service.IWmsLocationService;
import com.dg.wss.wms.service.IWmsShelfService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * create by chenjunsong
 * Date 2024/5/28 16:13
 * Description
 */

@Api(tags = "防错料")
@RestController
@RequestMapping("/anti/errorMatetial")
public class AntiErrorMatetialController extends BaseController {

    @Resource
    private IAntiErrorMaterialService antiErrorMaterialService;
    @Resource
    private IAntiMaterialOrderService antiMaterialOrderService;


    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> map) {
        startPage();
        TableDataInfo tableDataInfo = getDataTable(antiErrorMaterialService.list(map));
        return tableDataInfo;
    }


    @ApiOperation(value = "上料防错")
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody AntiErrorMaterial antiErrorMaterial) {
        antiErrorMaterial.setCreateBy(getUserId());
        antiErrorMaterial.setCircleId(getCircleId());
        return new AjaxResult(200,"操作成功",antiErrorMaterialService.add(antiErrorMaterial));
    }


    @ApiOperation(value = "查询上料记录-默认所有设备")
    @GetMapping("/getOrderAntiHistory")
    public TableDataInfo getOrderAntiHistory(@RequestParam Map<String,Object> map) {
        startPage();
        if (ObjectUtil.isEmpty(map.get("circleId"))) map.put("circleId",getCircleId());
        TableDataInfo tableDataInfo = getDataTable(antiMaterialOrderService.getOrderAntiHistory(map));
        return tableDataInfo;
    }

    @ApiOperation(value = "查询站位号")
    @GetMapping("/getOrderStationByOrderId")
    public TableDataInfo getOrderStationByOrderId(@RequestParam Long id) {
        startPage();
        TableDataInfo tableDataInfo = getDataTable(antiMaterialOrderService.getOrderStations(id));
        return tableDataInfo;
    }




    @ApiOperation(value = "根据设备获取最新生产的工单信息")
    @GetMapping("/getDeviceOrderByDeviceId")
    public AjaxResult getDeviceOrderByDeviceId(@RequestParam Long deviceId) {
        return antiMaterialOrderService.getDeviceOrderByDeviceId(deviceId,getCircleId());
    }







}
