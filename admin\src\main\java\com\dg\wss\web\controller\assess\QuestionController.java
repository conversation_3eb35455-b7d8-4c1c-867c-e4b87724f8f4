package com.dg.wss.web.controller.assess;

import com.dg.wss.assess.domain.Question;
import com.dg.wss.assess.domain.vo.QuestionVo;
import com.dg.wss.assess.service.IQuestionService;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.financial.domain.UserScore;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Api(tags = "学习任务-题目")
@RestController
@RequestMapping("/assess/question")
public class QuestionController extends BaseController {
    @Autowired
    private IQuestionService questionService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "examId", value = "考试id", dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "status", dataType = "string")})
    @DynamicResponseParameters(name = "question_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = Question[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<Question> list = questionService.pageByMap(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "根据examResultId, 获取题目列表,不包含答案")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "examId", value = "科目id", dataType = "Long")
    })
    @DynamicResponseParameters(name = "question_listNoAnswerByExam", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = Question[].class)
    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listNoAnswerByExam")
    public TableDataInfo listNoAnswerByExam(@RequestParam Long examResultId) {
        startPage();
        List<Question> list = questionService.listNoAnswerByExam(examResultId, getUserId());
        return getDataTable(list);
    }

    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "Question_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasPermi('assess:question:add')")
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@RequestBody Question question) {
        question.setCreateBy(getUserId());
        question.setCreateTime(new Date());
        return toAjax(questionService.insert(question));
    }

    @ApiOperation(value = "添加多个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "question_addlist", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasPermi('assess:question:add')")
    @RepeatSubmit
    @PostMapping("/addlist/{examId}")
    public AjaxResult addlist(@RequestBody List<Question> questions, @PathVariable Long examId) {
        return toAjax(questionService.insertList(getUserId(), examId, questions));
    }

    @ApiOperation(value = "生成题目")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "question_generatelist", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasPermi('assess:question:add')")
    @RepeatSubmit
    @PostMapping("/generatelist/{examId}")
    public AjaxResult generatelist(@PathVariable Long examId) {
        return questionService.generateList(getUserId(), examId);
    }

    @ApiOperation(value = "获取一题的结果,统计累计答对题数")
    @DynamicResponseParameters(name = "question_getResult", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = QuestionVo.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getResult")
    public AjaxResult selectById(@ApiParam("考试结果id") @RequestParam Long examResultId, @ApiParam("问题id") @RequestParam Long quesId, @ApiParam("用户选项，例如1,4") @RequestParam String answer, @ApiParam("题号,从1开始") Integer no) {
        return AjaxResult.success(questionService.getResult(examResultId, quesId, answer, no));
    }

    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "question_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasPermi('assess:question:edit')")
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody Question question) {
        question.setUpdateBy(getUserId());
        return toAjax(questionService.update(question));
    }

    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(name = "question_getInfo", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = Question.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(questionService.selectById(id));
    }

    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "Question_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasPermi('assess:question:remove')")
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(questionService.delete(id));
    }

    @ApiOperation(value = "随机抽一题")
    @DynamicResponseParameters(name = "UserScore_getInfo", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = UserScore.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/SampleQuestions")
    public AjaxResult SampleQuestions() {
        return AjaxResult.success(questionService.sampleQuestions());
    }
}
