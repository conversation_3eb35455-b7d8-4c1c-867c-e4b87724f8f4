package com.dg.wss.web.controller.aps;

import com.dg.wss.aps.domain.ApsUserMenu;
import com.dg.wss.aps.service.IApsUserMenuService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Map;
@Api(tags = "用户菜单绑定")
@RestController
@RequestMapping("/aps/apsUserMenu")
public class ApsUserMenuController extends BaseController {
    @Autowired
    private IApsUserMenuService apsUserMenuService;

    @ApiOperation(value = "统计人数")
    @GetMapping("/countUser")
    public AjaxResult countUser(String serviceType) {
        return AjaxResult.success(apsUserMenuService.countUser(getCircleId(),serviceType));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "menuPid", value = "一级菜单ID", dataType = "Sting"),
    })
    @ApiOperation(value = "二级菜单的用户列表")
    @GetMapping("/listUsersByMenuPid")
    public TableDataInfo listUsersByMenuPid(@RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        return getDataTable(apsUserMenuService.listAllUser(map));
    }

    @ApiOperation(value = "用户的菜单列表")
    @GetMapping("/listMenuByUser")
    public TableDataInfo listMenuByUser(@RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        map.put("userId", getUserId());
        return getDataTable(apsUserMenuService.listMenuByUser(map));
    }

    @ApiOperation(value = "用户的菜单列表")
    @GetMapping("/treeListMenuByUser")
    public TableDataInfo treeListMenuByUser(@RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        map.put("userId", getUserId());
        return getDataTable(apsUserMenuService.treeListMenuByUser(map));
    }

    @ApiOperation(value = "一级菜单绑定多个用户")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @RepeatSubmit
    @PostMapping("/insertUserList") //menuId, userIdList
    public AjaxResult insertUserList(@RequestBody ApsUserMenu vo) {
        vo.setCircleId(getCircleId());
        apsUserMenuService.insertUserList(vo);
        return success();
    }

    @ApiOperation(value = "二级菜单绑定多个用户")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @RepeatSubmit
    @PutMapping("/coverUserMenuList") //menuIdList, userId, menuId一级菜单
    public AjaxResult coverUserMenuList(@RequestBody ApsUserMenu vo) {
        vo.setCircleId(getCircleId());
        apsUserMenuService.coverUserMenuList(vo);
        return success();
    }

    /**
     * menuId一级菜单id
     * userId 被删除丶用户
     * @param apsUserMenu
     * @return
     */
    @ApiOperation(value = "删除用户菜单，包括一级菜单")
    @PutMapping("/delUser") //
    public AjaxResult delUser(@Validated @RequestBody ApsUserMenu apsUserMenu) {
        apsUserMenu.setCircleId(getCircleId());
        apsUserMenuService.delUser(apsUserMenu);
        return success();
    }

    @ApiOperation(value = "清空用户菜单，不包括一级菜单")
    @PutMapping("/clearUserMenu") //
    public AjaxResult clearUserMenu(@Validated @RequestBody ApsUserMenu apsUserMenu) {
        apsUserMenu.setCircleId(getCircleId());
        apsUserMenuService.clearUserMenu(apsUserMenu);
        return success();
    }

    @ApiOperation(value = "取消勾选用户菜单")
    @PutMapping("/uncheckUserMenu") //
    public AjaxResult uncheckUserMenu(@Validated @RequestBody ApsUserMenu apsUserMenu) {
        apsUserMenu.setCircleId(getCircleId());
        apsUserMenuService.uncheckUserMenu(apsUserMenu);
        return success();
    }

    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsUserMenu_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "用户菜单绑定", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ApsUserMenu apsUserMenu) {
        apsUserMenu.setCreateBy(getUserId());
        apsUserMenu.setCircleId(getCircleId());
        apsUserMenu.setCreateTime(new Date());
        return toAjax(apsUserMenuService.insert(apsUserMenu));
    }
    
    //@ApiOperation(value = "添加多个")
    //@ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    //@DynamicResponseParameters(name = "collateralRate_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            //@DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    //@PreAuthorize("@ss.hasRole('basics')")
    //@RepeatSubmit
    //@PostMapping("/addlist")
    //public AjaxResult addlist(@RequestBody List<ApsUserMenu> apsUserMenus) {
        //return toAjax(apsUserMenuService.batchInsert(apsUserMenus, getUserId()));
    //}
    
//    @ApiOperation(value = "修改")
//    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
//    @DynamicResponseParameters(name = "apsUserMenu_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
//            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
//    @PreAuthorize("@ss.hasRole('basics')")
//    @Log(title = "用户菜单绑定", businessType = BusinessType.UPDATE)
//    @RepeatSubmit
//    @PutMapping("/edit")
//    public AjaxResult edit(@RequestBody ApsUserMenu apsUserMenu) {
//        apsUserMenu.setUpdateBy(getUserId());
//        apsUserMenu.setUpdateTime(new Date());
//        return toAjax(apsUserMenuService.update(apsUserMenu));
//    }
    
    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ApsUserMenu.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(apsUserMenuService.selectById(id));
    }
    
    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "apsUserMenu_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "用户菜单绑定", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(apsUserMenuService.delete(id));
    }
}