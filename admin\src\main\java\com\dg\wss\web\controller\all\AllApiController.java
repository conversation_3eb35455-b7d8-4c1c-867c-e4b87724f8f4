package com.dg.wss.web.controller.all;

import com.dg.wss.all.domain.AllApi;
import com.dg.wss.all.domain.ApiApplication;
import com.dg.wss.all.service.IAllApiService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 系统api 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@RestController
@RequestMapping("/all/allApi")
public class AllApiController extends BaseController {
    @Autowired
    private IAllApiService allApiService;


    /**
     * 查询系统api列表
     */
    @ApiOperation(value = "查询系统api列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo list(@ModelAttribute AllApi allApi) {
        fillTime(allApi);
        startPage();
        List<AllApi> list = allApiService.listAllApi(allApi);
        return getDataTable(list);
    }

    @GetMapping("/listAllApiApplication")
    @ResponseBody
    public TableDataInfo listAllApiApplication(@ModelAttribute AllApi allApi) {
        fillTime(allApi);
        startPage();
        return getDataTable(allApiService.listAllApiApplication());
    }

    @GetMapping("/listAllApiApplicationInfo")
    @ResponseBody
    public AjaxResult listAllApiApplicationInfo(@ModelAttribute ApiApplication apiApplication) {
//        fillTime(allApi);
//        startPage();
        return AjaxResult.success(allApiService.listAllApiApplicationInfo(apiApplication));
    }

    /**
     * 查询系统api详情
     */
    @ApiOperation(value = "查询系统api详情")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult list(@RequestParam Long id) {
        return AjaxResult.success(allApiService.getById(id));
    }

    /**
    * 新增系统api信息
    */
    @ApiOperation(value = "新增系统api信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统api", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@RequestBody AllApi allApi){
        allApi.setCreateBy(getUserId());
        allApi.setCircleId(getCircleId());
        allApi.setCreateTime(new Date());
        boolean save = allApiService.save(allApi);
        return save?AjaxResult.success(allApi):AjaxResult.error();
    }

    /**
    * 批量保存系统api
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统api", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAdd")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchAdd(@RequestBody List<AllApi> allApis) {
        for (AllApi allApi : allApis) {
            allApi.setCircleId(getCircleId());
            allApi.setCreateBy(getUserId());
            allApi.setCreateTime(new Date());
        }
        return AjaxResult.success(allApiService.saveBatch(allApis));
    }

    /**
     * 修改系统api信息
     */
    @ApiOperation(value = "修改系统api信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统api", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@RequestBody AllApi allApi){
        allApi.setUpdateBy(getUserId());
        allApi.setCircleId(getCircleId());
        allApi.setUpdateTime(new Date());
        return allApiService.updateById(allApi)?AjaxResult.success():AjaxResult.error();
    }

    /**
    * 批量修改系统api
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统api", businessType = BusinessType.UPDATE)
    @PutMapping("/batchEdit")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchEdit(@RequestBody List<AllApi> allApis) {
        for (AllApi allApi : allApis) {
            allApi.setCircleId(getCircleId());
            allApi.setUpdateBy(getUserId());
            allApi.setUpdateTime(new Date());
        }
        return AjaxResult.success(allApiService.updateBatchById(allApis));
    }

    /**
     * 删除系统api信息
     */
    @ApiOperation(value = "删除系统api信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统api", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable String id){
        return AjaxResult.success(allApiService.removeById(id));
    }


    @ApiOperation(value = "调用系统api")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "调用系统api", businessType = BusinessType.INSERT)
    @PostMapping("/callApi")
    @RepeatSubmit
    public AjaxResult callApi(@RequestBody AllApi allApi, HttpServletRequest request){
        allApi.setCreateBy(getUserId());
        allApi.setCircleId(getCircleId());
        String res = allApiService.callApi(allApi,request);
        return AjaxResult.success(res);
    }

    /**
     * 查询性能最慢的API列表
     */
    @ApiOperation(value = "查询性能最慢的API列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listSlowestApis")
    @ResponseBody
    public AjaxResult listSlowestApis(@RequestParam(defaultValue = "20") Integer limit) {
        List<AllApi> list = allApiService.listSlowestApis(limit);
        return AjaxResult.success(list);
    }
}
