package com.dg.wss.web.controller.all;

import com.dg.wss.all.domain.AllApiResult;
import com.dg.wss.all.service.IAllApiResultService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 系统api的返回参数列表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@RestController
@RequestMapping("/allApiResult")
public class AllApiResultController extends BaseController {
    @Autowired
    private IAllApiResultService allApiResultService;


    /**
     * 查询系统api的返回参数列表列表
     */
    @ApiOperation(value = "查询系统api的返回参数列表列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo list(@ModelAttribute AllApiResult allApiResult) {
        fillTime(allApiResult);
        startPage();
        List<AllApiResult> list = allApiResultService.listAllApiResult(allApiResult);
        return getDataTable(list);
    }

    /**
     * 查询系统api的返回参数列表详情
     */
    @ApiOperation(value = "查询系统api的返回参数列表详情")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult list(@RequestParam Long id) {
        return AjaxResult.success(allApiResultService.getById(id));
    }

    /**
    * 新增系统api的返回参数列表信息
    */
    @ApiOperation(value = "新增系统api的返回参数列表信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统api的返回参数列表", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@RequestBody AllApiResult allApiResult){
        allApiResult.setCreateBy(getUserId());
//        allApiResult.setCircleId(getCircleId());
        allApiResult.setCreateTime(new Date());
        return AjaxResult.success(allApiResultService.save(allApiResult));
    }

    /**
    * 批量保存系统api的返回参数列表
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统api的返回参数列表", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAdd")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchAdd(@RequestBody List<AllApiResult> allApiResults) {
        for (AllApiResult allApiResult : allApiResults) {
//            allApiResult.setCircleId(getCircleId());
            allApiResult.setCreateBy(getUserId());
            allApiResult.setCreateTime(new Date());
        }
        return AjaxResult.success(allApiResultService.saveBatch(allApiResults));
    }

    /**
     * 修改系统api的返回参数列表信息
     */
    @ApiOperation(value = "修改系统api的返回参数列表信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统api的返回参数列表", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@RequestBody AllApiResult allApiResult){
//        allApiResult.setUpdateBy(getUserId());
//        allApiResult.setCircleId(getCircleId());
//        allApiResult.setUpdateTime(new Date());
        return AjaxResult.success(allApiResultService.updateById(allApiResult));
    }

    /**
    * 批量修改系统api的返回参数列表
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统api的返回参数列表", businessType = BusinessType.UPDATE)
    @PutMapping("/batchEdit")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchEdit(@RequestBody List<AllApiResult> allApiResults) {
        for (AllApiResult allApiResult : allApiResults) {
//            allApiResult.setCircleId(getCircleId());
//            allApiResult.setUpdateBy(getUserId());
//            allApiResult.setUpdateTime(new Date());
        }
        return AjaxResult.success(allApiResultService.updateBatchById(allApiResults));
    }

    /**
     * 删除系统api的返回参数列表信息
     */
    @ApiOperation(value = "删除系统api的返回参数列表信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "系统api的返回参数列表", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable String id){
        return AjaxResult.success(allApiResultService.removeById(id));
    }
}
