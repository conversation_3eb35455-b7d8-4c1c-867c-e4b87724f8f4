package com.dg.wss.web.controller.assess;


import com.dg.wss.assess.domain.Exam;
import com.dg.wss.assess.domain.vo.ExamVo;
import com.dg.wss.assess.service.IExamService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.domain.entity.SysRole;
import com.dg.wss.common.core.domain.entity.SysRoleVo;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.common.utils.Node2;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.system.mapper.SysUserRoleMapper;
import com.dg.wss.system.service.ISysRoleService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;

@Api(tags = "学习任务-管理")
@RestController
@RequestMapping("/assess/exam")
public class ExamController extends BaseController {

    @Autowired
    private IExamService examService;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;


    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "status", value = "1启用，0禁用", dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "changed", value = "是否有新题目", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "create_by", value = "创建人", dataType = "datetime")
    })
    @DynamicResponseParameters(name = "Exam_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = Exam[].class)
    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<Exam> list = examService.selectByMap(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "分页查询- 根据角色，查询所有学习")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "roleId", value = "1启用，0禁用", dataType = "string")
    })
    @DynamicResponseParameters(name = "Exam_listByRole", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = Exam[].class)
    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listByRole")
    public TableDataInfo listByRole(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<Exam> list = examService.selectByRole(map);
        return getDataTable(list);
    }


    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime", "changed", "questionNum", "status"})
    @DynamicResponseParameters(name = "Exam_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "学习任务-管理", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('assess:exam:add')")
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@RequestBody Exam Exam) {
        Exam.setCreateBy(getUserId());
        Exam.setCreateTime(new Date());
        return toAjax(examService.insert(Exam));
    }


    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "Exam_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "学习任务-管理", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('assess:exam:edit')")
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody Exam Exam) {
        Exam.setUpdateBy(getUserId());
        return toAjax(examService.update(Exam));
    }

    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(name = "exam_getInfo", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = Exam.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(examService.selectById(id));
    }


    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "Exam_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "学习任务-管理", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('assess:exam:remove')")
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(examService.delete(id));
    }

    @ApiOperation(value = "后台 获取学习任务的角色列表")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = SysRole[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/roleList")
    public TableDataInfo roleList(@RequestParam(required =false) String moduleType) {
        startPage();
        List<SysRoleVo> result = new ArrayList<>();
        List<SysRole> sysRoles;
        if (StringUtils.isNotBlank(moduleType)) {
            sysRoles = sysRoleService.selectStudyRoleAll(moduleType);
        } else {
            sysRoles = sysRoleService.selectStudyRoleAll();
        }
        for (SysRole sysRole : sysRoles) {
            SysRoleVo sysRoleVo = new SysRoleVo(sysRole);
            sysRoleVo.setStudentNum(sysUserRoleMapper.countNotDelUserByRoleId(sysRole.getRoleId()));
            // 角色对应的学习
            List<Node2<Long, String>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            map.put("roleId", sysRole.getRoleId());
            List<Exam> examList = examService.selectByRole(map);
            for (Exam e : examList) {
                list.add(new Node2(e.getId(), e.getName()));
            }
            sysRoleVo.setExamList(list);
            result.add(sysRoleVo);
        }
        TableDataInfo table = getDataTable(result);
        table.setTotal(getDataTable(sysRoles).getTotal());
        return table;
    }

    @ApiOperation(value = "前台 获取学习任务的 我的角色, 自定义角色+ 我的系统角色")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime", "admin", "delFlag", "menuCheckStrictly"})
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = SysRole[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/myRoleList")
    public TableDataInfo myRoleList(@RequestParam(required =false) String moduleType) {
        startPage();
        if (StringUtils.isNotBlank(moduleType)) {
            return getDataTable(examService.myRoleList(getUserId(), moduleType));
        }

        List<SysRoleVo> result = examService.myRoleList(getUserId());
        return getDataTable(result);
    }


    @ApiOperation(value = "绑定 角色 - 学习任务, 会覆盖掉之前的绑定")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "roleId", value = "角色id", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "examIds", value = "学习id 数组", dataType = "Long")
    })
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "学习任务-管理", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/roleBindExam")
    public AjaxResult bindRole(@ApiIgnore @RequestBody ExamVo examVo) {
        return toAjax(examService.roleBindExam(examVo.getRoleId(), examVo.getExamIds()));
    }

}
