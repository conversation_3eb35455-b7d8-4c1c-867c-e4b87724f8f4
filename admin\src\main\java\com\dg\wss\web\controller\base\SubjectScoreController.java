package com.dg.wss.web.controller.base;

import com.dg.wss.base.domain.SubjectScore;
import com.dg.wss.base.service.ISubjectScoreService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

@Api(tags = "习题积分规则")
@RestController
@RequestMapping("/base/subjectScore")
public class SubjectScoreController extends BaseController {

    @Autowired
    private ISubjectScoreService subjectScoreService;

    /**
     * 添加习题积分规则
     *
     * @return
     */
    @ApiOperation(value = "添加习题积分规则")
    @ApiOperationSupport(ignoreParameters = {"id", "userId", "status", "createTime", "updateTime"})
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody SubjectScore subjectScore) {
        subjectScore.setCreateBy(getUserId());
        return subjectScoreService.insertSubjectScore(subjectScore);
    }

    @ApiOperation(value = "修改习题积分规则")
    @ApiOperationSupport(ignoreParameters = {"version"})
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@Validated @RequestBody SubjectScore subjectScore) {
        subjectScore.setUpdateBy(getUserId());
        return subjectScoreService.updateSubjectScore(subjectScore);
    }

    //查询所有
    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "页数大小", dataType = "datetime"),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "页号", dataType = "datetime")})
    @DynamicResponseParameters(name = "subjectScore_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = SubjectScore[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<SubjectScore> list = subjectScoreService.selectSubjectScoreList(map);
        return getDataTable(list);
    }

    //查询单个习题积分规则
    @ApiOperation(value = "查询单个习题积分规则")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = SubjectScore.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("习题积分规则ID") Long id) {
        return AjaxResult.success(subjectScoreService.getSubjectScoreById(id));
    }

    //删除多个习题积分规则
    @ApiOperation(value = "删除多个习题积分规则")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = SubjectScore.class)})
    @Log(title = "习题积分规则", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasRole('basics')")
    @DeleteMapping("/remove")
    public AjaxResult remove(@ApiParam("id") Long id) {
        return toAjax(subjectScoreService.deleteSubjectScore(id));
    }
}
