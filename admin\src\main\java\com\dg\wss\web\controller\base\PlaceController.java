package com.dg.wss.web.controller.base;

import com.dg.wss.base.domain.Place;
import com.dg.wss.base.service.IPlaceService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

@Api(tags = "渠道")
@RestController
@RequestMapping("/base/place")
public class PlaceController extends BaseController {

    @Autowired
    private IPlaceService placeService;

    @ApiOperation(value = "添加")
    @DynamicResponseParameters(name = "Place_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@RequestBody Place place) {
        place.setCreateBy(getUsername());
        return toAjax(placeService.insert(place));
    }

    @ApiOperation(value = "修改")
    @DynamicResponseParameters(name = "Place_edit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@RequestBody Place place) {
        place.setUpdateBy(getUsername());
        return toAjax(placeService.update(place));
    }

    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "Place_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "渠道-delete", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('base:place:remove')")
    @DeleteMapping("/remove/{id}")
    @RepeatSubmit
    public AjaxResult delete(@PathVariable Long id) {
        return toAjax(placeService.delete(id));
    }

    @ApiOperation(value = "分页条件查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "user_id", value = "用户ID", dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "create_by", value = "创建人", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "update_by", value = "修改人", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "phonenumber", value = "手机号", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "organization_id", value = "实体ID", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "name", value = "姓名", dataType = "String")})
    @DynamicResponseParameters(name = "place_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = Place[].class)})
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<Place> list = placeService.selectList(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "id查询")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = Place.class)})
    @PreAuthorize("@ss.hasPermi('base:place:getInfo')")
    @GetMapping("/getInfo")
    public AjaxResult getInfo(Long id) {
        return AjaxResult.success(placeService.selectById(id));
    }
}
