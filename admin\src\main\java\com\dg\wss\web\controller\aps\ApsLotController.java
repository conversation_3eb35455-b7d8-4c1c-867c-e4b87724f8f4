package com.dg.wss.web.controller.aps;

import com.dg.wss.aps.domain.ApsLot;
import com.dg.wss.aps.domain.SimulateVo;
import com.dg.wss.aps.domain.vo.ApsLotVo;
import com.dg.wss.aps.service.IApsLotService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.domain.UnencryptedResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.common.utils.SecurityUtils;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.crm.domain.vo.CrmProductVO;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags = "aps lot")
@RestController
@RequestMapping("/aps/apsLot")
public class ApsLotController extends BaseController {
    @Autowired
    private IApsLotService apsLotService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", value = "id", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "circleId", value = "圈子id", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "batchOrderId", value = "批单id", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "name", value = "名称", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "no", value = "顺序", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "code", value = "编码", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "deliverDate", value = "交期", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "num", value = "数量", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "finishNum", value = "已完成数量", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型，aps", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "状态 1完成 0默认", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "createBy", value = "创建人", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "createTime", value = "创建时间", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "updateBy", value = "更新人", dataType = "Sting"),
            @ApiImplicitParam(paramType = "query", name = "updateTime", value = "更新时间", dataType = "Sting"),
    })
    @DynamicResponseParameters(name = "apsLot_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = ApsLot[].class)
    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listALL")
    public TableDataInfo listALL(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        List<ApsLot> list = apsLotService.listALL(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "分页查询列表和人机料法负荷情况")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listALLAndLoad")
    public TableDataInfo listALLAndLoad(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        map.put("organizationId", SecurityUtils.getLoginUser().getOrganizationId());
        List<ApsLot> list = apsLotService.listALLAndLoad(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "根据任务id查询它生成的lot单")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listByFromTask")
    public TableDataInfo listByFromTask(@RequestParam Long taskId) {
        List<ApsLot> ApsLots = apsLotService.listByFromTask(taskId);
        return getDataTable(ApsLots);
    }

    @ApiOperation(value = "根据异常单lotItem 查询它生成的lot单")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listByFromLotItem")
    public TableDataInfo listByFromLotItem(@RequestParam Long lotItemId) {
        List<ApsLot> ApsLots = apsLotService.listByFromLotItem(lotItemId);
        return getDataTable(ApsLots);
    }

    @ApiOperation(value = "根据销售单查lot")
    @GetMapping("/listByContractProduct")
    public TableDataInfo listByContractProduct(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        List<ApsLot> list = apsLotService.listByContractProduct(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "根据批单查lot")
    @GetMapping("/listByBatchOrder")
    public TableDataInfo listByBatchOrder(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        List<ApsLot> list = apsLotService.listByBatchOrder(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "根据令单查lot")
    @GetMapping("/listByWorkOrder")
    public TableDataInfo listByWorkOrder(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        List<ApsLot> list = apsLotService.listByWorkOrder(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "查询包含某个机器的未完成lot")
    @GetMapping("/listByResource")
    public TableDataInfo listByResource(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        List<ApsLot> list = apsLotService.listByResource(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "某个机器的未完成lot,批量替换工人or 机器")// 新机器id，旧机器id，lot id列表lotIdList，是否全选selectAll
    @Log(title = "批量替换工人或者机器", businessType = BusinessType.UPDATE)
    @PutMapping("/replaceResource")
    public AjaxResult replaceResource(@ApiIgnore @RequestBody Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        return apsLotService.replaceResource(map);
    }

    @ApiOperation(value = "根据令单查lot")
    @PostMapping("/simulate")
    public TableDataInfo listByWorkOrder(@RequestBody SimulateVo simulateVo) {
        List<CrmProductVO> productVOS = apsLotService.simulateByProductIdAndStartDate(simulateVo);
        return getDataTable(productVOS);
    }

    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsLot_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps lot", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ApsLot apsLot) {
        apsLot.setCreateBy(getUserId());
        apsLot.setCircleId(getCircleId());
        apsLot.setCreateTime(new Date());
        apsLot.setOrganizationId(SecurityUtils.getLoginUser().getOrganizationId());
        return toAjax(apsLotService.insert(apsLot));
    }

    @ApiOperation(value = "拆分lot")
    @Log(title = "拆分lot", businessType = BusinessType.UPDATE)
    @PostMapping("/split")
    public AjaxResult split(@Validated @RequestBody ApsLot apsLot) {
        apsLot.setCreateBy(getUserId());
        apsLot.setCircleId(getCircleId());
        apsLot.setCreateTime(new Date());
        apsLot.setOrganizationId(SecurityUtils.getLoginUser().getOrganizationId());
        return toAjax(apsLotService.split(apsLot));
    }

    @ApiOperation(value = "批单分lot")
    @PostMapping("/batchOrderLot")
    public AjaxResult batchOrderLot(@Validated @RequestBody ApsLot apsLot) {
//        apsLot.setCreateBy(getUserId());
//        apsLot.setCircleId(getCircleId());
        apsLot.setCreateTime(new Date());
//        return toAjax(apsLotService.batchOrderLot(apsLot.getBatchOrderId(),apsLot.getCircleId()));
        apsLotService.batchOrderLot(apsLot.getBatchOrderId(), getCircleId(), getOrganizationId(), getLoginUser().getUser());
        return AjaxResult.success();
    }

    @ApiOperation(value = "从lot单拆分lot")
    @PostMapping("/splitLot")
    public AjaxResult splitLot(@Validated @RequestBody ApsLotVo apsLot) {
        apsLotService.splitLot(apsLot);
        return AjaxResult.success();
    }

    @ApiOperation(value = "合并lot")
    @PostMapping("/mergeLot")
    public AjaxResult mergeLot(@Validated @RequestBody ApsLotVo apsLot) {
        List<ApsLot> lotList = apsLot.getLotList();
        List<Long> ids = lotList.stream().map(e -> e.getId()).collect(Collectors.toList());
        apsLotService.mergeLot(ids);
        return AjaxResult.success();
    }

    @ApiOperation(value = "排lot，选择多个lot id，生成工艺任务，分配到机器")
    @Log(title = "排程 aps planLot", businessType = BusinessType.INSERT)
    @PostMapping("/planLot")
    public AjaxResult planLot(@Validated @RequestBody ApsLot apsLot) {
        apsLot.setCreateBy(getUserId());
        apsLot.setCircleId(getCircleId());
        apsLot.setCreateTime(new Date());
        synchronized (this) {
            return toAjax(apsLotService.planLot(apsLot));
        }
    }

    @ApiOperation(value = "已排的lot可转为待排")
    @Log(title = "已排的lot可转为待排", businessType = BusinessType.INSERT)
    @PutMapping("/unPlanLot")
    public AjaxResult unPlanLot(@Validated @RequestBody ApsLot apsLot) {
        apsLot.setCreateBy(getUserId());
        apsLot.setCircleId(getCircleId());
        apsLot.setCreateTime(new Date());
        return toAjax(apsLotService.unPlanLot(apsLot.getId()));
    }

    //@ApiOperation(value = "添加多个")
    //@ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    //@DynamicResponseParameters(name = "collateralRate_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
    //@DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    //@PreAuthorize("@ss.hasRole('basics')")
    //@RepeatSubmit
    //@PostMapping("/addlist")
    //public AjaxResult addlist(@RequestBody List<ApsLot> apsLots) {
    //return toAjax(apsLotService.batchInsert(apsLots, getUserId()));
    //}

    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsLot_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps lot", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody ApsLot apsLot) {
        apsLot.setUpdateBy(getUserId());
        apsLot.setUpdateTime(new Date());
        apsLot.setOrganizationId(SecurityUtils.getLoginUser().getOrganizationId());
        return toAjax(apsLotService.update(apsLot));
    }

    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ApsLot.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(apsLotService.selectById(id));
    }

    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "apsLot_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps lot", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(apsLotService.delete(id));
    }

    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/viewReport")
    public UnencryptedResult viewReport(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        return UnencryptedResult.success(apsLotService.viewReport(map));
    }

    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/wipList")
    public UnencryptedResult wipList(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        return UnencryptedResult.success(apsLotService.getWipList(map));
    }

    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getAllWipList")
    public TableDataInfo getAllWipList(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        fillTimeForMap(map);
        map.put("circleId", getCircleId());
        return getDataTable(apsLotService.getAllWipList(map));
    }

    /**
     * LOT单进度
     */
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getLotOrderProgress")
    public AjaxResult getLotOrderProgress(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        fillTimeForMap(map);
        map.put("circleId", getCircleId());
        return AjaxResult.success(apsLotService.getLotOrderProgress(map));
    }


    /**
     * LOT单进度forRdc
     */
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getLotOrderProgressForRdc")
    public UnencryptedResult getLotOrderProgressForRdc(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        fillTimeForMap(map);
        map.put("circleId", getCircleId());
        return UnencryptedResult.success(apsLotService.getLotOrderProgressForRdc(map));
    }

    @ApiOperation(value = "扫码查对应工单by code")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "页码", dataType = "Sting",defaultValue = "1"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量", dataType = "Sting",defaultValue = "10"),
            @ApiImplicitParam(paramType = "query", name = "planed", value = "是否已排单：1已排 0默认", dataType = "Sting",defaultValue = "1"),
            @ApiImplicitParam(paramType = "query", name = "sourceCode", value = "溯源号", dataType = "Sting",required = true),

    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/selectByCode")
    public TableDataInfo selectByCode(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        if(!map.containsKey("pageNum")){
            map.put("pageNum", 1);
        }
        if(!map.containsKey("pageSize")){
            map.put("pageSize", 10);
        }
        if(!map.containsKey("planed")){
            map.put("planed", 1);
        }
        // 如果列表为空，则生成的工单没有自动单独排程的配置
        List<ApsLot> list = apsLotService.selectBySourceCode(map);
        return getDataTable(list);
    }
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getLotIdsBySourceCode")
    public TableDataInfo getLotIdsBySourceCode(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        if(!map.containsKey("pageNum")){
            map.put("pageNum", 1);
        }
        if(!map.containsKey("pageSize")){
            map.put("pageSize", 2000);
        }
        if(!map.containsKey("planed")){
            map.put("planed", 1);
        }
        // 如果列表为空，则生成的工单没有自动单独排程的配置
        List<Long> list = apsLotService.getLotIdsBySourceCode(map);
        return getDataTable(list);
    }
    @ApiOperation(value = "明细数据，导出Excel")
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/exportExcelDetail")
    public AjaxResult exportExcelDetail(@RequestBody Map<String, Object> map) {
        return AjaxResult.success(apsLotService.exportExcelDetail(getCircleId(),getUserId(),map));
    }
}