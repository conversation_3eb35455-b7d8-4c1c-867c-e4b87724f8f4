package com.dg.wss.web.controller.attendance;

import com.dg.wss.attendance.domain.AttendanceRequestConfig;
import com.dg.wss.attendance.service.IAttendanceRequestConfigService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@Api(tags = "考勤申请配置")
@RestController
@RequestMapping("/attendance/requestConfig")
public class AttendanceRequestConfigController extends BaseController {
    @Autowired
    private IAttendanceRequestConfigService requestConfigService;

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ModelAttribute AttendanceRequestConfig requestConfig) {
        startPage();
        requestConfig.setCircleId(getCircleId());
        List<AttendanceRequestConfig> list = requestConfigService.listConfig(requestConfig);
        return getDataTable(list);
    }


    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "考勤申请配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody AttendanceRequestConfig requestConfig) {
        requestConfig.setUpdateBy(getUserId());
        requestConfig.setUpdateTime(new Date());
        requestConfig.setCircleId(getCircleId());
        return toAjax(requestConfigService.update(requestConfig));
    }

    @ApiOperation(value = "查询by id")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(requestConfigService.selectById(id));
    }

    @ApiOperation(value = "删除")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "考勤申请配置", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(requestConfigService.delete(id));
    }
}