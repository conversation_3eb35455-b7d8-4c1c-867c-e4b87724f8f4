package com.dg.wss.web.controller.attendance;

import com.dg.wss.attendance.domain.vo.WorkTimeInRestRuleVo;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.common.utils.StringUtils;
import com.dg.wss.commonservice.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestBody;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.attendance.domain.WorkTimeInRestRule;
import com.dg.wss.attendance.service.IWorkTimeInRestRuleService;
import com.dg.wss.common.core.domain.AjaxResult;



/**
 * 排班外工时计算规则Controller
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Controller
@Api(tags = "排班外工时计算规则")
@RequestMapping("/attendance/workTimeInRestRule")
public class WorkTimeInRestRuleController extends BaseController
{
    private String prefix = "attendance/workTimeInRestRule";

    @Autowired
    private IWorkTimeInRestRuleService workTimeInRestRuleService;

    /**
     * 查询排班外工时计算规则列表
     */
    @ApiOperation(value = "查询排班外工时计算规则列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(@RequestBody WorkTimeInRestRule workTimeInRestRule)
    {
        startPage();
        workTimeInRestRule.setCircleId(this.getCircleId());
        List<WorkTimeInRestRuleVo> list = workTimeInRestRuleService.selectWorkTimeInRestRuleList(workTimeInRestRule);
        return getDataTable(list);
    }

    /**
     * 查询排班外工时计算规则
     */
    @ApiOperation(value = "查询排班外工时计算规则")
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/getById")
    @ResponseBody
    public AjaxResult getById(@RequestParam(name = "id",required = true) Long id)
    {
        return AjaxResult.success(workTimeInRestRuleService.selectWorkTimeInRestRuleById(id));
    }

    /**
     * 导出排班外工时计算规则列表
     */
    //@RequiresPermissions("attendance:workTimeInRestRule:export")
    @ApiOperation(value = "导出排班外工时计算规则列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "排班外工时计算规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(WorkTimeInRestRule workTimeInRestRule)
    {
        List<WorkTimeInRestRuleVo> list = workTimeInRestRuleService.selectWorkTimeInRestRuleList(workTimeInRestRule);
        ExcelUtil<WorkTimeInRestRuleVo> util = new ExcelUtil<WorkTimeInRestRuleVo>(WorkTimeInRestRuleVo.class);
        return util.exportExcel(list, "排班外工时计算规则数据");
    }

    /**
     * 新增保存排班外工时计算规则
     */
    @ApiOperation(value = "新增保存排班外工时计算规则")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "排班外工时计算规则", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody WorkTimeInRestRuleVo workTimeInRestRule)
    {
        return toAjax(workTimeInRestRuleService.insertWorkTimeInRestRule(workTimeInRestRule));
    }

    /**
     * 修改保存排班外工时计算规则
     */
    @ApiOperation(value = "修改保存排班外工时计算规则")
    //@RequiresPermissions("attendance:workTimeInRestRule:edit")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "排班外工时计算规则", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody WorkTimeInRestRuleVo workTimeInRestRule)
    {
        return toAjax(workTimeInRestRuleService.updateWorkTimeInRestRule(workTimeInRestRule));
    }

    /**
     * 删除排班外工时计算规则
     */
    //@RequiresPermissions("attendance:workTimeInRestRule:remove")
    @ApiOperation(value = "删除排班外工时计算规则,ids,以逗号分割")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "排班外工时计算规则", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(@RequestParam(value = "ids", required = true)  String ids)
    {
        return toAjax(workTimeInRestRuleService.deleteWorkTimeInRestRuleByIds(ids));
    }
}
