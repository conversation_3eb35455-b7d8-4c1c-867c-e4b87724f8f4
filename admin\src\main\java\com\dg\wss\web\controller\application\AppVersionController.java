package com.dg.wss.web.controller.application;

import com.dg.wss.application.domain.AppComponent;
import com.dg.wss.application.domain.AppPage;
import com.dg.wss.application.domain.AppVersion;
import com.dg.wss.application.service.IAppVersionService;
import com.dg.wss.application.service.IApplicationService;
import com.dg.wss.application.vo.AppComponentVersionQueryVo;
import com.dg.wss.application.vo.AppVersionQueryVo;
import com.dg.wss.application.vo.ApplicationVo;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.core.validate.CreateGroup;
import com.dg.wss.common.core.validate.UpdateGroup;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.groups.Default;
import java.util.List;

@RestController
@RequestMapping("/application/version")
@Api("应用版本管理")
public class AppVersionController extends BaseController {
    @Autowired
    private IAppVersionService appVersionService;

    @PostMapping
    @Log(title = "新增应用版本", businessType = BusinessType.INSERT)
    @ApiOperation("新增应用版本")
    public AjaxResult create(@RequestBody @Validated({Default.class, CreateGroup.class}) AppVersion appVersion) {
        appVersionService.createAppVersion(appVersion);
        return AjaxResult.success("创建应用版本成功",appVersion);
    }

    //修改应用版本
    @PostMapping("/update")
    @ApiOperation("修改应用版本")
    @Log(title = "修改应用版本", businessType = BusinessType.UPDATE)
    public AjaxResult update(@RequestBody @Validated ({Default.class, UpdateGroup.class}) AppVersion appVersion) {
        appVersionService.updateAppVersion(appVersion);
        return AjaxResult.success("修改应用版本成功",appVersion);
    }

    //删除应用版本
    @DeleteMapping("/delete/{id}")
    @ApiOperation("删除应用版本")
    @Log(title = "删除应用版本", businessType = BusinessType.DELETE)
    public AjaxResult delete(@PathVariable Long id) {
        appVersionService.deleteAppVersionById(id);
        return AjaxResult.success("删除应用版本成功");
    }

    @GetMapping("/list")
    @ApiOperation("分页查询")
    public TableDataInfo list(AppVersionQueryVo appVersionQueryVo) {
        startPage();
        return getDataTable(appVersionService.listAppVersions(appVersionQueryVo));
    }

    @GetMapping("/get/{id}")
    @ApiOperation("获取应用版本")
    public AjaxResult get(@PathVariable Long id) {
        AppVersion appVersion = appVersionService.getAppVersionById(id);
        if (appVersion == null) {
            return AjaxResult.error("应用版本不存在");
        }
        return AjaxResult.success("获取应用版本成功",appVersion);
    }

    @GetMapping("/queryAppComponentByAppKeyAndVersion")
    @ApiOperation("根据条件获取应用版本下的组件列表，可以根据组件key，组件类型，组件父级key进行查询")
    public AjaxResult list(AppComponentVersionQueryVo queryVo) {
        List<AppComponent> appComponentVersion = appVersionService.selectAppComponents(queryVo);
        return AjaxResult.success("获取应用版本列表成功",appComponentVersion);
    }
}
