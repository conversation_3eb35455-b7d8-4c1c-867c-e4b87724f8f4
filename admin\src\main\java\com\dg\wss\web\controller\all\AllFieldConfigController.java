package com.dg.wss.web.controller.all;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;

import com.dg.wss.dic.domain.AllFieldConfig;
import com.dg.wss.dic.service.IAllFieldConfigService;

import java.util.Date;
import java.util.List;

import org.springframework.web.bind.annotation.RestController;
import com.dg.wss.commonservice.BaseController;

/**
 * <p>
 * 字段配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@RestController
@RequestMapping("/allFieldConfig")
public class AllFieldConfigController extends BaseController {
    @Autowired
    private IAllFieldConfigService aAllFieldConfigService;


    /**
     * 查询字段配置列表
     */
    @ApiOperation(value = "查询字段配置列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(@RequestBody AllFieldConfig param) {
        //fillTime(param);
       // startPage();
        List<AllFieldConfig> list = aAllFieldConfigService.selectAllFieldConfigList(param);
        return getDataTable(list);
    }

    /**
     * 查询字段配置详情
     */
    @ApiOperation(value = "查询字段配置详情")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult list(@RequestParam Long id) {
        return AjaxResult.success(aAllFieldConfigService.getById(id));
    }

    /**
    * 新增字段配置信息
    */
    @ApiOperation(value = "新增字段配置信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "字段配置", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@RequestBody AllFieldConfig param){
        param.setCreateBy(getUserId());
       // param.setCircleId(getCircleId());
        param.setCreateTime(new Date());
        return AjaxResult.success(aAllFieldConfigService.save(param));
    }

    /**
    * 批量修改保存scm检验项目
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "scm检验项目", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAdd")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchAdd(@RequestBody List<AllFieldConfig> paramList) {
        for (AllFieldConfig param : paramList) {
            param.setCircleId(getCircleId());
            param.setCreateBy(getUserId());
            param.setCreateTime(new Date());
        }
        return AjaxResult.success(aAllFieldConfigService.saveBatch(paramList));
    }

    /**
     * 修改字段配置信息
     */
    @ApiOperation(value = "修改字段配置信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "字段配置", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@RequestBody AllFieldConfig param){
        param.setUpdateBy(getUserId());
        param.setCircleId(getCircleId());
        param.setUpdateTime(new Date());
        return AjaxResult.success(aAllFieldConfigService.updateById(param));
    }

    /**
    * 批量修改字段配置
    */
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "字段配置", businessType = BusinessType.UPDATE)
    @PutMapping("/batchEdit")
    @RepeatSubmit
    @ResponseBody
    public AjaxResult batchEdit(@RequestBody List<AllFieldConfig> paramList) {
        for (AllFieldConfig param : paramList) {
            param.setCircleId(getCircleId());
            param.setUpdateBy(getUserId());
            param.setUpdateTime(new Date());
        }
        return AjaxResult.success(aAllFieldConfigService.updateBatchById(paramList));
    }

    /**
     * 删除字段配置信息
     */
    @ApiOperation(value = "删除字段配置信息")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "字段配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable String id){
        return AjaxResult.success(aAllFieldConfigService.removeById(id));
    }
}
