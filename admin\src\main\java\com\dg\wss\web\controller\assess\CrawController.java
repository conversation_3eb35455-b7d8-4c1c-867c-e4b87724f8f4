package com.dg.wss.web.controller.assess;

import com.dg.wss.assess.domain.Craw;
import com.dg.wss.assess.domain.vo.CrawVo;
import com.dg.wss.assess.service.ICrawService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "内容网站")
@RestController
@RequestMapping("/assess/craw")
public class CrawController extends BaseController {

    @Autowired
    private ICrawService crawService;

    @ApiOperation(value = "添加内容网站")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "craw_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "内容网站", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('assess:craw:add')")
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody Craw Craw) {
        return crawService.insertCraw(Craw);
    }

    @ApiOperation(value = "修改内容网站")
    @ApiOperationSupport(ignoreParameters = {"", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "craw_edit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "内容网站", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('assess:craw:edit')")
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@Validated @RequestBody Craw craw) {
        return toAjax(crawService.updateCraw(craw));
    }

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "type", value = "es类型", dataType = "Sting")})
    @DynamicResponseParameters(name = "craw_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = CrawVo[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> map) {
        startPage();
        List<CrawVo> list = crawService.selectCrawList(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "id查询")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = Craw.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("id") @RequestParam Long id) {
        return AjaxResult.success(crawService.getCrawById(id));
    }

    @ApiOperation(value = "删除内容网站")
    @DynamicResponseParameters(properties = {
            @DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "内容网站", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasRole('basics')")
    @DeleteMapping("/remove")
    public AjaxResult remove(@RequestBody Craw craw) {
        return toAjax(crawService.deleteCraw(craw));
    }
}
