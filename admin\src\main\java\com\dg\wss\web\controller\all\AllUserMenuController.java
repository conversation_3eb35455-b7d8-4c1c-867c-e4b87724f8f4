package com.dg.wss.web.controller.all;

import com.dg.wss.all.domain.AllUserMenu;
import com.dg.wss.all.service.IAllMenuService;
import com.dg.wss.all.service.IAllUserMenuService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Api(tags = "用户菜单绑定")
@RestController
@RequestMapping("/all/allUserMenu")
public class AllUserMenuController extends BaseController {
    @Autowired
    private IAllUserMenuService allUserMenuService;

    @ApiOperation(value = "统计人数")
    @GetMapping("/countUser")
    public AjaxResult countUser(String serviceType) {
        return AjaxResult.success(allUserMenuService.countUser(getCircleId(),serviceType));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "menuPid", value = "一级菜单ID", dataType = "Sting"),
    })
    @ApiOperation(value = "二级菜单的用户列表")
    @GetMapping("/listUsersByMenuPid")
    public TableDataInfo listUsersByMenuPid(@RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        return getDataTable(allUserMenuService.listAllUser(map));
    }

    @ApiOperation(value = "用户的菜单列表")
    @GetMapping("/listMenuByUser")
    public TableDataInfo listMenuByUser(@RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        map.put("userId", getUserId());
        return getDataTable(allUserMenuService.listMenuByUser(map));
    }

    @ApiOperation(value = "用户的菜单列表")
    @GetMapping("/treeListMenuByUser")
    public TableDataInfo treeListMenuByUser(@RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        map.put("userId", getUserId());
        return getDataTable(allUserMenuService.treeListMenuByUser(map));
    }

    @ApiOperation(value = "返回用户列表，附带每个用户有的菜单列表")
    @GetMapping("/listAllUserByMenuType")
    public TableDataInfo listAllUserByMenuType(@RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
//        map.put("userId", getUserId());
        return getDataTable(allUserMenuService.listAllUserByMenuType(map));
    }

    @ApiOperation(value = "一级菜单绑定多个用户")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @RepeatSubmit
    @PostMapping("/insertUserList") //menuId, userIdList
    public AjaxResult insertUserList(@RequestBody AllUserMenu vo) {
        vo.setCircleId(getCircleId());
        allUserMenuService.insertUserList(vo);
        return success();
    }

    @ApiOperation(value = "二级菜单绑定多个用户")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @RepeatSubmit
    @PutMapping("/coverUserMenuList") //menuIdList, userId, menuId一级菜单
    public AjaxResult coverUserMenuList(@RequestBody AllUserMenu vo) {
        vo.setCircleId(getCircleId());
        allUserMenuService.coverUserMenuList(vo);
        return success();
    }


    @PutMapping("/deleteUserMenu") //
    public AjaxResult deleteUserMenu(@Validated @RequestBody AllUserMenu AllUserMenu) {
        AllUserMenu.setCircleId(getCircleId());
        allUserMenuService.deleteUserMenu(AllUserMenu);
        return success();
    }

    /**
     * menuId一级菜单id
     * userId 被删除丶用户
     * @param AllUserMenu
     * @return
     */
    @ApiOperation(value = "删除用户菜单，包括一级菜单")
    @PutMapping("/delUser") //
    public AjaxResult delUser(@Validated @RequestBody AllUserMenu AllUserMenu) {
        AllUserMenu.setCircleId(getCircleId());
        allUserMenuService.delUser(AllUserMenu);
        return success();
    }

    @ApiOperation(value = "清空用户菜单，不包括一级菜单")
    @PutMapping("/clearUserMenu") //
    public AjaxResult clearUserMenu(@Validated @RequestBody AllUserMenu AllUserMenu) {
        AllUserMenu.setCircleId(getCircleId());
        allUserMenuService.clearUserMenu(AllUserMenu);
        return success();
    }

    @ApiOperation(value = "取消勾选用户菜单")
    @PutMapping("/uncheckUserMenu") //
    public AjaxResult uncheckUserMenu(@Validated @RequestBody AllUserMenu AllUserMenu) {
        AllUserMenu.setCircleId(getCircleId());
        allUserMenuService.uncheckUserMenu(AllUserMenu);
        return success();
    }

    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "AllUserMenu_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "用户菜单绑定", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody AllUserMenu AllUserMenu) {
        AllUserMenu.setCreateBy(getUserId());
        AllUserMenu.setCircleId(getCircleId());
        AllUserMenu.setCreateTime(new Date());
        return toAjax(allUserMenuService.insert(AllUserMenu));
    }
    
    //@ApiOperation(value = "添加多个")
    //@ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    //@DynamicResponseParameters(name = "collateralRate_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            //@DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    //@PreAuthorize("@ss.hasRole('basics')")
    //@RepeatSubmit
    //@PostMapping("/addlist")
    //public AjaxResult addlist(@RequestBody List<AllUserMenu> AllUserMenus) {
        //return toAjax(allUserMenuService.batchInsert(AllUserMenus, getUserId()));
    //}
    
    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "AllUserMenu_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "用户菜单绑定", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody AllUserMenu AllUserMenu) {
        AllUserMenu.setUpdateBy(getUserId());
        AllUserMenu.setUpdateTime(new Date());
        return toAjax(allUserMenuService.update(AllUserMenu));
    }

    @ApiOperation(value = "修改多个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "AllUserMenu_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "用户菜单绑定", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/batchEdit")
    public AjaxResult batchEdit(@RequestBody List<AllUserMenu> allUserMenus) {
        for (AllUserMenu allUserMenu : allUserMenus) {
            allUserMenu.setCircleId(getCircleId());
            allUserMenu.setUpdateBy(getUserId());
            allUserMenu.setUpdateTime(new Date());
        }
        allUserMenuService.batchUpdate(allUserMenus);
        return AjaxResult.success();
    }
    
    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AllUserMenu.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(allUserMenuService.selectById(id));
    }
    
    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "AllUserMenu_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "用户菜单绑定", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(allUserMenuService.delete(id));
    }
}