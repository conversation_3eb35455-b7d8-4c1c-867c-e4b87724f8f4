package com.dg.wss.web.controller.application;

import com.dg.wss.application.domain.AppPage;
import com.dg.wss.application.service.IAppPageService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.core.validate.CreateGroup;
import com.dg.wss.common.core.validate.UpdateGroup;
import com.dg.wss.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.dg.wss.commonservice.BaseController;

import javax.validation.groups.Default;

@RestController
@RequestMapping("/application/page")
@Api("页面管理")
public class AppPageController extends BaseController {

    @Autowired
    private IAppPageService appPageService;

    @GetMapping("/list")
    @ApiOperation("分页查询")
    public TableDataInfo list(AppPage page) {
        startPage();
        return getDataTable(appPageService.queryAppPage(page));
    }

    @PostMapping
    @ApiOperation("新增页面")
    @Log(title = "新增页面", businessType = BusinessType.INSERT)
    @RepeatSubmit
    public AjaxResult add(@RequestBody @Validated({Default.class, CreateGroup.class}) AppPage page) {
        appPageService.createAppPage(page);
        return AjaxResult.success("新增页面成功", page);
    }

    @PutMapping
    @ApiOperation("修改页面")
    @Log(title = "修改页面", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    public AjaxResult edit(@RequestBody @Validated({Default.class, UpdateGroup.class}) AppPage page) {
        appPageService.updateAppPage(page);

        return AjaxResult.success("修改页面成功", page);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除页面")
    @Log(title = "删除页面", businessType = BusinessType.DELETE)
    public AjaxResult remove(@PathVariable Long id) {
        appPageService.removeAppPage(id);

        return AjaxResult.success("页面删除成功");
    }


    @DeleteMapping("removeByKey/{key}")
    @ApiOperation("根据页面key删除页面")
    @Log(title = "根据页面key删除页面", businessType = BusinessType.DELETE)
    public AjaxResult remove(@PathVariable String key) {
        appPageService.removeAppPageByKey(key);
        return AjaxResult.success("页面删除成功");
    }
}
