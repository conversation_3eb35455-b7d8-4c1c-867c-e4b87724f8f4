package com.dg.wss.web.controller.aps;

import com.dg.wss.aps.domain.ApsProcessLotConfig;
import com.dg.wss.aps.service.IApsProcessLotConfigService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Date;
import java.util.List;
import java.util.Map;
@Api(tags = "工艺的lot机时工时配置")
@RestController
@RequestMapping("/aps/apsProcessLotConfig")
public class ApsProcessLotConfigController extends BaseController {
    @Autowired
    private IApsProcessLotConfigService apsProcessLotConfigService;
    
    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
                 @ApiImplicitParam(paramType = "query", name = "id", value = "主键ID", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "circleId", value = "圈子", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "processId", value = "工艺id", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "lotNum", value = "lot数", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "machineTime", value = "机器用时，分钟", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "manTime", value = "人用时", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "loadingTime", value = "上下料", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "scanCardRequire", value = "是否扫卡", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "qcRequire", value = "是否质检", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createBy", value = "创建人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createTime", value = "创建时间", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateBy", value = "更新人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateTime", value = "更新时间", dataType = "Sting"),
    })
    @DynamicResponseParameters(name = "apsProcessLotConfig_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = ApsProcessLotConfig[].class)
    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        List<ApsProcessLotConfig> list = apsProcessLotConfigService.pageByMap(map);
        return getDataTable(list);
    }
    
    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsProcessLotConfig_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "工艺的lot机时工时配置", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ApsProcessLotConfig apsProcessLotConfig) {
        apsProcessLotConfig.setCreateBy(getUserId());
        apsProcessLotConfig.setCircleId(getCircleId());
        apsProcessLotConfig.setCreateTime(new Date());
        return toAjax(apsProcessLotConfigService.insert(apsProcessLotConfig));
    }
    
    //@ApiOperation(value = "添加多个")
    //@ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    //@DynamicResponseParameters(name = "collateralRate_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            //@DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    //@PreAuthorize("@ss.hasRole('basics')")
    //@RepeatSubmit
    //@PostMapping("/addlist")
    //public AjaxResult addlist(@RequestBody List<ApsProcessLotConfig> apsProcessLotConfigs) {
        //return toAjax(apsProcessLotConfigService.batchInsert(apsProcessLotConfigs, getUserId()));
    //}
    
    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsProcessLotConfig_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "工艺的lot机时工时配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody ApsProcessLotConfig apsProcessLotConfig) {
        apsProcessLotConfig.setUpdateBy(getUserId());
        apsProcessLotConfig.setUpdateTime(new Date());
        return toAjax(apsProcessLotConfigService.update(apsProcessLotConfig));
    }
    
    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ApsProcessLotConfig.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(apsProcessLotConfigService.selectById(id));
    }
    
    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "apsProcessLotConfig_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "工艺的lot机时工时配置", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(apsProcessLotConfigService.delete(id));
    }
}