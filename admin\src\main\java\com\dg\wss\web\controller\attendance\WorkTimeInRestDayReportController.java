package com.dg.wss.web.controller.attendance;

import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.common.utils.StringUtils;
import com.dg.wss.commonservice.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestBody;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.attendance.domain.WorkTimeInRestDayReport;
import com.dg.wss.attendance.service.IWorkTimeInRestDayReportService;
import com.dg.wss.common.core.domain.AjaxResult;



/**
 * 排班外工时统计日报表Controller
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Controller
@Api(tags = "排班外工时统计日报表")
@RequestMapping("/attendance/workTimeInRestDayReport")
public class WorkTimeInRestDayReportController extends BaseController
{
    private String prefix = "attendance/workTimeInRestDayReport";

    @Autowired
    private IWorkTimeInRestDayReportService workTimeInRestDayReportService;

    /**
     * 查询排班外工时统计日报表列表
     */
    @ApiOperation(value = "查询排班外工时统计日报表列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(@RequestBody WorkTimeInRestDayReport workTimeInRestDayReport)
    {
        startPage();
        List<WorkTimeInRestDayReport> list = workTimeInRestDayReportService.selectWorkTimeInRestDayReportList(workTimeInRestDayReport);
        return getDataTable(list);
    }

    /**
     * 查询排班外工时统计日报表
     */
    @ApiOperation(value = "查询排班外工时统计日报表")
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/getById")
    @ResponseBody
    public AjaxResult getById(@RequestParam(name = "id",required = true) Long id)
    {
        return AjaxResult.success(workTimeInRestDayReportService.selectWorkTimeInRestDayReportById(id));
    }

    /**
     * 导出排班外工时统计日报表列表
     */
    //@RequiresPermissions("attendance:workTimeInRestDayReport:export")
    @ApiOperation(value = "导出排班外工时统计日报表列表")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "排班外工时统计日报表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(WorkTimeInRestDayReport workTimeInRestDayReport)
    {
        List<WorkTimeInRestDayReport> list = workTimeInRestDayReportService.selectWorkTimeInRestDayReportList(workTimeInRestDayReport);
        ExcelUtil<WorkTimeInRestDayReport> util = new ExcelUtil<WorkTimeInRestDayReport>(WorkTimeInRestDayReport.class);
        return util.exportExcel(list, "排班外工时统计日报表数据");
    }

    /**
     * 新增保存排班外工时统计日报表
     */
    @ApiOperation(value = "新增保存排班外工时统计日报表")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "排班外工时统计日报表", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody WorkTimeInRestDayReport workTimeInRestDayReport)
    {
        return toAjax(workTimeInRestDayReportService.insertWorkTimeInRestDayReport(workTimeInRestDayReport));
    }

    /**
     * 修改保存排班外工时统计日报表
     */
    @ApiOperation(value = "修改保存排班外工时统计日报表")
    //@RequiresPermissions("attendance:workTimeInRestDayReport:edit")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "排班外工时统计日报表", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody WorkTimeInRestDayReport workTimeInRestDayReport)
    {
        return toAjax(workTimeInRestDayReportService.updateWorkTimeInRestDayReport(workTimeInRestDayReport));
    }

    /**
     * 删除排班外工时统计日报表
     */
    //@RequiresPermissions("attendance:workTimeInRestDayReport:remove")
    @ApiOperation(value = "删除排班外工时统计日报表,ids,以逗号分割")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "排班外工时统计日报表", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(@RequestParam(value = "ids", required = true)  String ids)
    {
        return toAjax(workTimeInRestDayReportService.deleteWorkTimeInRestDayReportByIds(ids));
    }
}
