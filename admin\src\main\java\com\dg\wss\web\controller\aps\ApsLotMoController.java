package com.dg.wss.web.controller.aps;

import com.dg.wss.aps.domain.ApsLotMo;
import com.dg.wss.aps.service.IApsLotMoService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.domain.UnencryptedResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.scm.domain.vo.ScmProductSourceVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Api(tags = "aps lot_mo")
@RestController
@RequestMapping("/aps/apsLotMo")
public class ApsLotMoController extends BaseController {
    @Autowired
    private IApsLotMoService apsLotMoService;
    
    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
                 @ApiImplicitParam(paramType = "query", name = "id", value = "主键ID", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "lotId", value = "lot id", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "moId", value = "mo id", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "num", value = "数量", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createBy", value = "创建人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createTime", value = "创建时间", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateBy", value = "更新人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateTime", value = "更新时间", dataType = "Sting"),
    })
    @DynamicResponseParameters(name = "apsLotMo_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = ApsLotMo[].class)
    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        List<ApsLotMo> list = apsLotMoService.pageByMap(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "根据批单查询lot")
    @GetMapping("/listByBatchOrder")
    public TableDataInfo listByBatchOrder(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        List<ApsLotMo> list = apsLotMoService.listByBatchOrder(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "根据令单查询lot")
    @GetMapping("/listByWorkOrder")
    public TableDataInfo listByWorkOrder(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        List<ApsLotMo> list = apsLotMoService.listByWorkOrder(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "根据任务id查询lot")
    @GetMapping("/listMIByTask")
    public TableDataInfo listMIByTask(@ApiIgnore @RequestParam Long taskId) {
        startPage();
        List<ApsLotMo> list = apsLotMoService.listMIByTask(taskId);
        return getDataTable(list);
    }

    @ApiOperation(value = "根据lot查询全MI, 参数lotId")
    @GetMapping("/listMIByLot")
    public TableDataInfo listMIByLot(@ApiIgnore @RequestParam Map<String,Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        List<ApsLotMo> list = apsLotMoService.listMIByLot(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "保存人工调整后的任务分配")
    @PostMapping("/saveApsLotTasks")
    public AjaxResult saveApsLotTasks(@RequestBody ApsLotMo apsLotMo) {
        apsLotMo.setUpdateBy(getUserId());
        apsLotMo.setUpdateTime(new Date());
        return toAjax(apsLotMoService.saveApsLotTasks(apsLotMo));
    }

    @ApiOperation(value = "根据lot查询来源信息")
    @GetMapping("/sourceByLot")
    public TableDataInfo sourceByLot(@RequestParam Long lotId, @RequestParam Long moId) {
        startPage();
        List<ScmProductSourceVo> sourceByLots = apsLotMoService.getSourceByLot(lotId, moId);
        return getDataTable(sourceByLots);
    }

    @ApiOperation(value = "根据lot查询全MI, 只查询子件, 参数lotId")
    @GetMapping("/listSubProductMIByLot")
    public TableDataInfo listSubProductMIByLot(@ApiIgnore @RequestParam Map<String,Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        List<ApsLotMo> list = apsLotMoService.listSubProductMIByLot(map);
        return getDataTable(list);
    }


    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listMIByLot/viewReport")
    public UnencryptedResult viewReport(@ModelAttribute ApsLotMo apsLotMo) {
//        startPage();
        Map<String, Object> map = new HashMap<>();
        map.put("lotId", apsLotMo.getLotId());
        map.put("lotIds", apsLotMo.getLotIds());
        return UnencryptedResult.success(apsLotMoService.listMIByLotReport(map));
    }


    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsLotMo_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps lot_mo", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ApsLotMo apsLotMo) {
        apsLotMo.setCreateBy(getUserId());
        apsLotMo.setCreateTime(new Date());
        return toAjax(apsLotMoService.insert(apsLotMo));
    }
    
    //@ApiOperation(value = "添加多个")
    //@ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    //@DynamicResponseParameters(name = "collateralRate_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            //@DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    //@PreAuthorize("@ss.hasRole('basics')")
    //@RepeatSubmit
    //@PostMapping("/addlist")
    //public AjaxResult addlist(@RequestBody List<ApsLotMo> apsLotMos) {
        //return toAjax(apsLotMoService.batchInsert(apsLotMos, getUserId()));
    //}
    
    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsLotMo_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps lot_mo", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody ApsLotMo apsLotMo) {
        apsLotMo.setUpdateBy(getUserId());
        apsLotMo.setUpdateTime(new Date());
        return toAjax(apsLotMoService.update(apsLotMo));
    }
    
    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ApsLotMo.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(apsLotMoService.selectById(id));
    }
    
    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "apsLotMo_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps lot_mo", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(apsLotMoService.delete(id));
    }
}