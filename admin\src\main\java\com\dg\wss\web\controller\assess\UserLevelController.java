package com.dg.wss.web.controller.assess;

import com.dg.wss.assess.domain.UserLevel;
import com.dg.wss.assess.service.IUserLevelService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户等级
 */
@Api(tags = "用户等级")
@RestController
@RequestMapping("/assess/userLevel")
public class UserLevelController extends BaseController {

    @Autowired
    private IUserLevelService userLevelService;

    @ApiOperation(value = "添加用户等级")
    @ApiOperationSupport(ignoreParameters = {"id", "status", "createdTime", "createdBy"})
    @DynamicResponseParameters(properties = {
            @DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasPermi('assess:userLevel:add')")
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody UserLevel userLevel) {
        userLevel.setCreatedBy(getUserId());
        return toAjax(userLevelService.insertUserLevel(userLevel));
    }

    @ApiOperation(value = "修改用户等级")
    @ApiOperationSupport(ignoreParameters = {"createdTime", "createdBy"})
    @DynamicResponseParameters(properties = {
            @DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasPermi('assess:userLevel:edit')")
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@Validated @RequestBody UserLevel userLevel) {
        return toAjax(userLevelService.updateUserLevel(userLevel));
    }

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "title", value = "任务名称", dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "订单id", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "datetime"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "datetime")})
    @DynamicResponseParameters(name = "task_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = UserLevel[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> map) {
        startPage();
        List<UserLevel> list = userLevelService.selectUserLevelList(map);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('assess:userLevel:getInfo')")
    @GetMapping("/getInfo")
    public AjaxResult getInfo(Long id) {
        return AjaxResult.success(userLevelService.selectUserLevelById(id));
    }

    @Log(title = "用户等级", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('assess:userLevel:remove')")
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(userLevelService.deleteUserLevelByIds(id));
    }
}
