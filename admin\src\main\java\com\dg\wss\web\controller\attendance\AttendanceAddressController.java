package com.dg.wss.web.controller.attendance;

import com.dg.wss.attendance.domain.AttendanceAddress;
import com.dg.wss.attendance.service.IAttendanceAddressService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Api(tags = "考勤地点")
@RestController
@RequestMapping("/attendance/attendanceAddress")
public class AttendanceAddressController extends BaseController {
    @Autowired
    private IAttendanceAddressService attendanceAddressService;

    @ApiOperation(value = "添加考勤地点")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime"})
    @DynamicResponseParameters(name = "attendanceAddress_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/add")
    @RepeatSubmit
    @Log(title = "考勤地点", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated @RequestBody AttendanceAddress attendanceAddress) {
        attendanceAddress.setCreateBy(getUserId());
        attendanceAddress.setCircleId(getCircleId());
        return toAjax(attendanceAddressService.addAttendanceAddress(attendanceAddress));
    }

    @ApiOperation(value = "编辑考勤地点")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime"})
    @DynamicResponseParameters(name = "attendanceAddress_edit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/edit")
    @RepeatSubmit
    @Log(title = "考勤地点", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated @RequestBody AttendanceAddress attendanceAddress) {
        return toAjax(attendanceAddressService.editAttendanceAddress(attendanceAddress));
    }

    @ApiOperation(value = "删除考勤地点")
    @DynamicResponseParameters(name = "attendanceAddress_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @DeleteMapping("/remove")
    @RepeatSubmit
    @Log(title = "考勤地点", businessType = BusinessType.DELETE)
    public AjaxResult delete(@Validated Long id) {
        return toAjax(attendanceAddressService.deleteAttendanceAddress(id));
    }

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "Sting")})
    @DynamicResponseParameters(name = "attendanceAddress_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = AttendanceAddress[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> map) {
        filterCircle(map);
        startPage();
        return getDataTable(attendanceAddressService.list(map));
    }

    @ApiOperation(value = "id查询")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AttendanceAddress.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("id") @RequestParam Long id) {
        return checkCircle(attendanceAddressService.getInfo(id));
    }
}
