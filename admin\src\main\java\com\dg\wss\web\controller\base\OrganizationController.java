package com.dg.wss.web.controller.base;

import com.dg.wss.base.domain.Organization;
import com.dg.wss.base.domain.vo.DiversionChannelVo;
import com.dg.wss.base.service.IOrganizationService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

@Api(tags = "实体组织")
@RestController
@RequestMapping("/base/organization")
public class OrganizationController extends BaseController {
    @Autowired
    private IOrganizationService OrganizationService;

    @ApiOperation(value = "添加")
    @DynamicResponseParameters(name = "Organization_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime", "managerList"})
    @Log(title = "实体组织", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@RequestBody Organization Organization) {
        try {
            Organization.setCreateBy(getUsername());
            return toAjax(OrganizationService.insert(Organization, getUserId()));
        } catch (SQLException e) {
            logger.error("添加实体组织失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "修改")
    @DynamicResponseParameters(name = "Organization_edit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @Log(title = "实体组织", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasRole('basics')")
    @PutMapping("/edit")
    @RepeatSubmit
    public AjaxResult edit(@RequestBody Organization Organization) {

        try {
            Organization.setUpdateBy(getUsername());
            return toAjax(OrganizationService.update(Organization));
        } catch (SQLException e) {
            logger.error("修改实体异常", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "Organization_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "实体组织", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('base:Organization:remove')")
    @DeleteMapping("/remove/{id}")
    @RepeatSubmit
    public AjaxResult delete(@PathVariable Long id) {
        return toAjax(OrganizationService.delete(id));
    }


    @ApiOperation(value = "分页条件查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "userId", value = "用户ID", dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "createBy", value = "创建人", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "updateBy", value = "修改人", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "phonenumber", value = "手机号", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "organizationId", value = "实体ID", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "name", value = "姓名", dataType = "String")
    })
    @DynamicResponseParameters(name = "Organization_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = Organization[].class)
    })
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        if (!isHaveRole("admin") && !isHaveRole("htgly")) map.put("userId", getUserId());
        List<Organization> list = OrganizationService.selectList(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "实体设置 分页查询 附带管理人信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", value = "id", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "createBy", value = "创建人", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "updateBy", value = "修改人", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "fullName", value = "全称", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "name", value = "简称", dataType = "String")
    })
    @DynamicResponseParameters(name = "Organization_list_manager", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = Organization[].class)
    })
    @GetMapping("/list_manager")
    public TableDataInfo listWithManager(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<Organization> list = OrganizationService.selectListManager(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "id查询")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = Organization.class)})
    @PreAuthorize("@ss.hasPermi('base:Organization:getInfo')")
    @GetMapping("/getInfo")
    public AjaxResult getInfo(Long id) {
        return AjaxResult.success(OrganizationService.selectById(id));
    }

    //分渠的list查找
    @ApiOperation(value = "分渠的list查找")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "cmId", value = "商务id", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "placeId", value = "渠道id", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "nickName", value = "昵称(模糊查找)", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "页数", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "cmName", value = "商务名字(模糊查找)", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "placeName", value = "渠道名字(模糊查找)", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "大小", dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "organizationId", value = "组织id", dataType = "String")
    })
    @DynamicResponseParameters(name = "organization_diversionChannel", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = DiversionChannelVo[].class)
    })
    @PreAuthorize("@ss.hasPermi('base:Organization:diversionChannelList')")
    @GetMapping("/diversionChannel")
    public TableDataInfo diversionChannel(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<DiversionChannelVo> list = OrganizationService.diversionChannelList(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "修改分渠")
    @DynamicResponseParameters(name = "organization_edit", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @Log(title = "修改分渠", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('base:Organization:diversionChannelEdit')")
    @PutMapping("/diversionChannel")
    @RepeatSubmit
    public AjaxResult diversionChannel(@RequestBody DiversionChannelVo diversionChannelVo) {
        try {
            return toAjax(OrganizationService.diversionChannelEdit(diversionChannelVo));
        } catch (SQLException e) {
            logger.error("修改分渠失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }
}
