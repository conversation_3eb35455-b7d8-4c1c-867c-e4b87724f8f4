package com.dg.wss.web.controller.aiq;

import com.dg.wss.aiq.domain.AiqLotItem;
import com.dg.wss.aiq.domain.AiqLotItemVo;
import com.dg.wss.aiq.domain.LotItemVo;
import com.dg.wss.aiq.service.IAiqLotItemService;
import com.dg.wss.aps.domain.ApsActivity;
import com.dg.wss.aps.domain.ApsActivityVo;
import com.dg.wss.aps.service.IApsActivityService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.domain.UnencryptedResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Api(tags = "aiq质检")
@RestController
@RequestMapping("/aiq/aiqLotItem")
public class AiqLotItemController extends BaseController {
    @Autowired
    private IAiqLotItemService lotItemService;

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<AiqLotItem> list = lotItemService.pageByMap(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/listByLot")
    public TableDataInfo listByLot(@ApiIgnore @ModelAttribute AiqLotItemVo aiqLotItemVo) {
        startPage();
        List<AiqLotItemVo> list = lotItemService.selectListByLot(aiqLotItemVo.getCircleId()
                , aiqLotItemVo.getProductId(), aiqLotItemVo.getLotId(), aiqLotItemVo.getTaskId(),
                aiqLotItemVo.getScanCardId(), aiqLotItemVo.getProcessId());
        return getDataTable(list);
    }

    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aiq质检", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody AiqLotItem aiqLotItem) {
        aiqLotItem.setCreateBy(getUserId());
        aiqLotItem.setCreateTime(new Date());
        return toAjax(lotItemService.insert(aiqLotItem));
    }

    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aiq质检", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody AiqLotItem aiqLotItem) {
        return toAjax(lotItemService.update(aiqLotItem));
    }

    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aiq质检", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/addOrEdit")
    public AjaxResult addOrEdit(@RequestBody List<AiqLotItem> aiqLotItems) {
        return toAjax(lotItemService.insertOrUpdate(aiqLotItems));
    }

    @PreAuthorize("@ss.hasRole('basics')")
    @RepeatSubmit
    @PostMapping("/getOtherTask")
    public AjaxResult getOtherTask(@RequestBody List<AiqLotItem> aiqLotItems) {
        return AjaxResult.success(lotItemService.getOtherTask(aiqLotItems));
    }
    
    @PreAuthorize("@ss.hasRole('basics')")
    @RepeatSubmit
    @PostMapping("/syncOtherCheck")
    public AjaxResult syncOtherCheck(@RequestBody LotItemVo lotItemVo) {
        return AjaxResult.success(lotItemService.syncOtherCheck(lotItemVo.getAiqLotItems(), lotItemVo.getApsTasks()));
    }

    @ApiOperation(value = "查询by id")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(lotItemService.selectById(id));
    }

    @ApiOperation(value = "删除")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aiq质检", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(lotItemService.delete(id));
    }

    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/viewReport")
    public UnencryptedResult viewReport(@RequestParam Long id) {
        startPage();
        Map<String, Object> data = lotItemService.viewReport(id);
        return UnencryptedResult.success(data);
    }

    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/checkQc")
    public AjaxResult checkQc(@RequestParam Long taskId,
                              @RequestParam(required = false) Long scanCardId,
                                     @RequestParam String scanStatus) {
//        startPage();
        boolean checkQc = lotItemService.checkQc(taskId, scanCardId, scanStatus);
        return toAjax(checkQc);
    }
}