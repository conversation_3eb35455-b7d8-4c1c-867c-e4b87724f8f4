package com.dg.wss.web.controller.attendance;

import com.dg.wss.attendance.domain.GroupManager;
import com.dg.wss.attendance.domain.vo.GroupManagerVo;
import com.dg.wss.attendance.service.IGroupManagerService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "考勤管理人员")
@RestController
@RequestMapping("/attendance/groupManager")
public class GroupManagerController extends BaseController {
    @Autowired
    private IGroupManagerService groupManagerService;

    @ApiOperation(value = "添加考勤管理人员")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime"})
    @DynamicResponseParameters(name = "groupManager_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @PostMapping("/add")
    @RepeatSubmit
    @Log(title = "考勤管理员", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated @RequestBody List<GroupManagerVo> managers) {
        if (managers.isEmpty()) return AjaxResult.error("请选中管理人员");
        return toAjax(groupManagerService.addManagers(getUserId(), managers, null));
    }

    @ApiOperation(value = "删除考勤管理人员")
    @DynamicResponseParameters(name = "groupManager_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @DeleteMapping("/remove")
    @RepeatSubmit
    @Log(title = "考勤管理员", businessType = BusinessType.DELETE)
    public AjaxResult delete(@Validated Long id) {
        return toAjax(groupManagerService.deleteGroupManager(id));
    }

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", name = "type", value = "类型", dataType = "Sting")})
    @DynamicResponseParameters(name = "groupManager_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = GroupManagerVo[].class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> map) {
        filterCircle(map);
        startPage();
        return getDataTable(groupManagerService.list(map));
    }

    @ApiOperation(value = "id查询")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = GroupManager.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("id") @RequestParam Long id) {
        return checkCircle(groupManagerService.getInfo(id));
    }
}
