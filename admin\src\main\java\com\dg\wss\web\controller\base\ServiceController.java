package com.dg.wss.web.controller.base;

import com.dg.wss.base.domain.Service;
import com.dg.wss.base.service.IServiceService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "服务内容")
@RestController
@RequestMapping("/base/service")
public class ServiceController extends BaseController {

    @Autowired
    private IServiceService serviceService;

    @ApiOperation(value = "添加")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "服务内容", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('base:service:add')")
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody Service service) {
        return toAjax(serviceService.insertService(service));
    }

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", name = "name", value = "姓名", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "no", value = "编号", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "phone", value = "手机号", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "beginTime", value = "开始时间", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "Long")})
    @DynamicResponseParameters(name = "Service_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = Service[].class)})
    @PreAuthorize("@ss.hasPermi('base:service:list')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> map) {
        startPage();
        List<Service> list = serviceService.selectService(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "服务内容", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('base:service:edit')")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody Service service) {
        return toAjax(serviceService.updateService(service));

    }

    @ApiOperation(value = "id查询")
    @DynamicResponseParameters(name = "Service_getInfo", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = Service.class)})
    @PreAuthorize("@ss.hasPermi('base:service:getInfo')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(Long id) {
        return AjaxResult.success(serviceService.selectServiceById(id));
    }

    @ApiOperation(value = "删除")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @Log(title = "服务内容", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('base:service:remove')")
    @DeleteMapping("/remove")
    public AjaxResult remove(@RequestBody Service service) {
        return toAjax(serviceService.deleteService(service));
    }
}
