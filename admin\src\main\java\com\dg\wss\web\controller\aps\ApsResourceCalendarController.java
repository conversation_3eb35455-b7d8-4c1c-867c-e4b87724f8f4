package com.dg.wss.web.controller.aps;

import com.dg.wss.aps.domain.ApsResourceCalendar;
import com.dg.wss.aps.service.IApsResourceCalendarService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Api(tags = "aps 资源日历")
@RestController
@RequestMapping("/aps/apsResourceCalendar")
public class ApsResourceCalendarController extends BaseController {
    @Autowired
    private IApsResourceCalendarService apsResourceCalendarService;
    
    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
                 @ApiImplicitParam(paramType = "query", name = "id", value = "主键ID", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "resourceId", value = "执行机器或者人员", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "calendarId", value = "日历", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createBy", value = "创建人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createTime", value = "创建时间", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateBy", value = "更新人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateTime", value = "更新时间", dataType = "Sting"),
    })
    @DynamicResponseParameters(name = "apsResourceCalendar_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = ApsResourceCalendar[].class)
    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        List<ApsResourceCalendar> list = apsResourceCalendarService.pageByMap(map);
        return getDataTable(list);
    }
    
    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsResourceCalendar_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps 资源日历", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ApsResourceCalendar apsResourceCalendar) {
        apsResourceCalendar.setCreateBy(getUserId());
        apsResourceCalendar.setCreateTime(new Date());
        return toAjax(apsResourceCalendarService.insert(apsResourceCalendar));
    }

    @ApiOperation(value = "从ehr同步资源日历")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "从ehr同步资源日历", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @GetMapping("/addByEhr")
    public AjaxResult addByEhr(@RequestParam(required = false) Integer days) {
        return toAjax(apsResourceCalendarService.insertByEhr(getCircleId(), days));
    }

    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsResourceCalendar_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps 资源日历", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody ApsResourceCalendar apsResourceCalendar) {
        apsResourceCalendar.setUpdateBy(getUserId());
        apsResourceCalendar.setUpdateTime(new Date());
        return toAjax(apsResourceCalendarService.update(apsResourceCalendar));
    }

    @ApiOperation(value = "批量设置多个资源的日历")
    @PutMapping("/batchUpdate")
    public AjaxResult batchUpdate(@Validated @RequestBody ApsResourceCalendar apsMachineCalendar) {
        apsMachineCalendar.setCreateBy(getUserId());
        apsMachineCalendar.setCreateTime(new Date());
        return toAjax(apsResourceCalendarService.batchUpdate(apsMachineCalendar));
    }
    
    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ApsResourceCalendar.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(apsResourceCalendarService.selectById(id));
    }
    
    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "apsResourceCalendar_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps 资源日历", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(apsResourceCalendarService.delete(id));
    }
}