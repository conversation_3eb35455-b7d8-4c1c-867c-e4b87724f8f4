package com.dg.wss.web.controller.aps;

import cn.hutool.core.date.DateUtil;
import com.dg.wss.aps.domain.ApsTaskResource;
import com.dg.wss.aps.domain.vo.ApsResourceTasksVo;
import com.dg.wss.aps.domain.vo.ApsTaskResourceVo;
import com.dg.wss.aps.service.IApsTaskResourceService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.web.controller.aps.vo.ResourceTaskVo;
import com.dg.wss.web.controller.aps.vo.ResourceTasksVo;
import com.dg.wss.web.controller.aps.vo.ResourceVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
@Api(tags = "aps任务task机器安排表")
@RestController
@RequestMapping("/aps/apsTaskResource")
public class ApsTaskResourceController extends BaseController {
    @Autowired
    private IApsTaskResourceService apsTaskResourceService;
    
    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
                 @ApiImplicitParam(paramType = "query", name = "id", value = "主键ID", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "taskId", value = "任务", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "resourceId", value = "执行机器 or 人员", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "type", value = "1机器  2人员", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "num", value = "数量", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createBy", value = "创建人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createTime", value = "创建时间", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateBy", value = "更新人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateTime", value = "更新时间", dataType = "Sting"),
    })
    @DynamicResponseParameters(name = "apsTaskResource_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = ApsTaskResource[].class)
    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        List<ApsTaskResource> list = apsTaskResourceService.pageByMap(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "日历页面，查询任务")
    @GetMapping("/listVo")
    public TableDataInfo listVo(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        List<ApsTaskResource> list = apsTaskResourceService.pageVoByMap(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "根据资源查询任务")
    @GetMapping("/listTasksByResource")
    public TableDataInfo listTasksByResource(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        List<ApsTaskResourceVo> list = apsTaskResourceService.listResourceByMap(map);
        List<ResourceTasksVo> resourceTasksVos = new ArrayList<>();
        String fmt = "yyyy-MM-dd HH:mm:ss";
        for(ApsTaskResourceVo taskResourceVo : list){
            ResourceTasksVo tasksVo = new ResourceTasksVo();
            ResourceVo resourceVo = getResourceVo(taskResourceVo);
            tasksVo.setResource(resourceVo);
            map.put("resourceId", taskResourceVo.getResourceId());
            List<ApsResourceTasksVo> apsResourceTasksVos = apsTaskResourceService.listTasksByResourceByMap(map);
            List<ResourceTaskVo> resourceTasks = new ArrayList<>();
            for(ApsResourceTasksVo tasksVo1 : apsResourceTasksVos){
                ResourceTaskVo resourceTaskVo = new ResourceTaskVo();
                resourceTaskVo.setId(tasksVo1.getTaskId());
                resourceTaskVo.setParent(taskResourceVo.getResourceId());
                resourceTaskVo.setName(tasksVo1.getProcessName());
                BeanUtils.copyProperties(tasksVo1, resourceTaskVo);
                resourceTaskVo.setStartTime(DateUtil.format(tasksVo1.getStartTime(), fmt));
                resourceTaskVo.setEndTime(DateUtil.format(tasksVo1.getEndTime(), fmt));
                resourceTaskVo.setColor("");
                resourceTasks.add(resourceTaskVo);
            }
            tasksVo.setResourceTasks(resourceTasks);
            resourceTasksVos.add(tasksVo);
        }
        return getDataTable(resourceTasksVos);
    }

    @NotNull
    private static ResourceVo getResourceVo(ApsTaskResourceVo taskResourceVo) {
        ResourceVo resourceVo  = new ResourceVo();
        resourceVo.setId(taskResourceVo.getResourceId());
        resourceVo.setName(taskResourceVo.getResourceName());
        resourceVo.setTypeCode(taskResourceVo.getTypeCode());
        resourceVo.setTypeName(taskResourceVo.getTypeName());
        resourceVo.setType(taskResourceVo.getResourceType());
        resourceVo.setSn(taskResourceVo.getSn());
        resourceVo.setSpace(taskResourceVo.getSpace());
        resourceVo.setWorkType(taskResourceVo.getWorkType());
        resourceVo.setLoad("90");
        resourceVo.setRender("split");
        return resourceVo;
    }

    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsTaskResource_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps任务task机器安排表", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ApsTaskResource apsTaskResource) {
        apsTaskResource.setCreateBy(getUserId());
        apsTaskResource.setCreateTime(new Date());
        return toAjax(apsTaskResourceService.insert(apsTaskResource));
    }
    
    //@ApiOperation(value = "添加多个")
    //@ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    //@DynamicResponseParameters(name = "collateralRate_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            //@DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    //@PreAuthorize("@ss.hasRole('basics')")
    //@RepeatSubmit
    //@PostMapping("/addlist")
    //public AjaxResult addlist(@RequestBody List<ApsTaskResource> apsTaskResources) {
        //return toAjax(apsTaskResourceService.batchInsert(apsTaskResources, getUserId()));
    //}
    
    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsTaskResource_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps任务task机器安排表", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody ApsTaskResource apsTaskResource) {
        apsTaskResource.setUpdateBy(getUserId());
        apsTaskResource.setUpdateTime(new Date());
        return toAjax(apsTaskResourceService.update(apsTaskResource));
    }
    
    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ApsTaskResource.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(apsTaskResourceService.selectById(id));
    }
    
    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "apsTaskResource_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps任务task机器安排表", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(apsTaskResourceService.delete(id));
    }
}