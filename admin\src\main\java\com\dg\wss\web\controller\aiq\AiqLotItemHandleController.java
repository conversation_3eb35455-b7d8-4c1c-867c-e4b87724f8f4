package com.dg.wss.web.controller.aiq;

import com.dg.wss.aiq.domain.AiqLotItemHandle;
import com.dg.wss.aiq.service.IAiqLotItemHandleService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Api(tags = "aiq质检")
@RestController
@RequestMapping("/aiq/aiqLotItemHandle")
public class AiqLotItemHandleController extends BaseController {
    @Autowired
    private IAiqLotItemHandleService lotItemHandleService;

    @ApiOperation(value = "分页查询")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId", getCircleId());
        List<AiqLotItemHandle> list = lotItemHandleService.pageByMap(map);
        return getDataTable(list);
    }

    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aiq质检", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody AiqLotItemHandle aiqLotItemHandle) {
        aiqLotItemHandle.setCreateBy(getUserId());
        aiqLotItemHandle.setCreateTime(new Date());
        aiqLotItemHandle.setCircleId(getCircleId());
        return toAjax(lotItemHandleService.insert(aiqLotItemHandle));
    }

    @ApiOperation(value = "添加单个并拆分lot")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aiq质检", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/commitAndRhc")
    public AjaxResult commitAndRhc(@Validated @RequestBody AiqLotItemHandle aiqLotItemHandle) {
        aiqLotItemHandle.setCreateBy(getUserId());
        aiqLotItemHandle.setCreateTime(new Date());
        aiqLotItemHandle.setCircleId(getCircleId());
        return toAjax(lotItemHandleService.commitAndRhc(aiqLotItemHandle));
    }

    @ApiOperation(value = "添加多个并拆分lot")
    @RepeatSubmit
    @PostMapping("/batchCommitAndRhc")
    public AjaxResult batchCommitAndRhc(@Validated @RequestBody List<AiqLotItemHandle> aiqLotItemHandle) {
//        aiqLotItemHandle.setCreateBy(getUserId());
//        aiqLotItemHandle.setCreateTime(new Date());
//        aiqLotItemHandle.setCircleId(getCircleId());
        return toAjax(lotItemHandleService.batchCommitAndRhc(aiqLotItemHandle));
    }

    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aiq质检", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody AiqLotItemHandle aiqLotItemHandle) {
        aiqLotItemHandle.setUpdateBy(getUserId());
        aiqLotItemHandle.setUpdateTime(new Date());
        return toAjax(lotItemHandleService.update(aiqLotItemHandle));
    }

    @ApiOperation(value = "查询by id")
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(lotItemHandleService.selectById(id));
    }

    @ApiOperation(value = "删除")
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aiq质检", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(lotItemHandleService.delete(id));
    }
}