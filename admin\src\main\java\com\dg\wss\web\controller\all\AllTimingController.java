package com.dg.wss.web.controller.all;

import com.dg.wss.all.domain.AllTiming;
import com.dg.wss.all.service.IAllTimingService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Date;
import java.util.List;
import java.util.Map;
@Api(tags = "任务计时统计")
@RestController
@RequestMapping("/all/allTiming")
public class AllTimingController extends BaseController {
    @Autowired
    private IAllTimingService allTimingService;
    
    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
                 @ApiImplicitParam(paramType = "query", name = "id", value = "主键ID", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "ownerId", value = "计时任务id", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "ownerType", value = "计时任务类型", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createBy", value = "创建人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createTime", value = "创建时间", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateBy", value = "更新人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateTime", value = "更新时间", dataType = "Sting"),
    })
    @DynamicResponseParameters(name = "allTiming_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = AllTiming[].class)
    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        List<AllTiming> list = allTimingService.pageByMap(map);
        return getDataTable(list);
    }
    
    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "allTiming_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "任务计时统计", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody AllTiming allTiming) {
        allTiming.setCreateBy(getUserId());
        allTiming.setCircleId(getCircleId());
        allTiming.setCreateTime(new Date());
        return toAjax(allTimingService.insert(allTiming));
    }
    
    //@ApiOperation(value = "添加多个")
    //@ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    //@DynamicResponseParameters(name = "collateralRate_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            //@DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    //@PreAuthorize("@ss.hasRole('basics')")
    //@RepeatSubmit
    //@PostMapping("/addlist")
    //public AjaxResult addlist(@RequestBody List<AllTiming> allTimings) {
        //return toAjax(allTimingService.batchInsert(allTimings, getUserId()));
    //}
    
    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "allTiming_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "任务计时统计", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody AllTiming allTiming) {
        allTiming.setUpdateBy(getUserId());
        allTiming.setUpdateTime(new Date());
        return toAjax(allTimingService.update(allTiming));
    }
    
    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = AllTiming.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(allTimingService.selectById(id));
    }
    
    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "allTiming_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "任务计时统计", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(allTimingService.delete(id));
    }
}