package com.dg.wss.web.controller.application;

import com.dg.wss.application.vo.ApplicationVo;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.core.validate.CreateGroup;
import com.dg.wss.common.core.validate.UpdateGroup;
import com.dg.wss.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.dg.wss.application.service.IApplicationService;
import com.dg.wss.commonservice.BaseController;

import javax.validation.groups.Default;

@RestController
@RequestMapping("/api/application")
@Api("应用管理")
public class ApplicationController extends BaseController {

    @Autowired
    private IApplicationService applicationService;

    @PostMapping
    @Log(title = "新增应用", businessType = BusinessType.INSERT)
    @ApiOperation("新增应用")
    public AjaxResult create(@RequestBody @Validated({CreateGroup.class, Default.class}) ApplicationVo application) {
        applicationService.createApplication(application);
        return AjaxResult.success("创建应用成功",application);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取应用详情")
    public AjaxResult get(@PathVariable Long id) {
        return AjaxResult.success("获取应用详情成功",applicationService.getById(id));
    }

    @GetMapping("getByKey/{appKey}")
    @ApiOperation("获取应用详情")
    public AjaxResult getByKey(@PathVariable String appKey) {
        return AjaxResult.success("获取应用详情成功",applicationService.getByAppKey(appKey));
    }

    @GetMapping("/list")
    @ApiOperation("分页查询应用列表")
    public TableDataInfo list() {
        startPage();
        return getDataTable(applicationService.listByCreateUser());
    }

    @GetMapping("/selectAppListByDeveloper")
    @ApiOperation("根据开发人员查询应用列表")
    public AjaxResult selectAppListByDeveloper() {
        return AjaxResult.success("查询应用成功",applicationService.selectAppListByDeveloper(getUserId()));
    }

    @PostMapping("/update/{id}")
    @Log(title = "修改应用属性", businessType = BusinessType.UPDATE)
    @ApiOperation("修改应用")
    public AjaxResult update(@RequestBody @Validated ({Default.class, UpdateGroup.class}) ApplicationVo application) {
        applicationService.updateApplication(application);
        return AjaxResult.success("更新应用成功",application);
    }

    @DeleteMapping("/removeByAppKey/{key}")
    @ApiOperation("删除应用")
    @Log(title = "删除应用", businessType = BusinessType.DELETE)
    public AjaxResult removeByAppKey(@PathVariable String key) {
        applicationService.deleteApplicationByAppKey(key);
        return AjaxResult.success("删除应用成功");
    }
}
