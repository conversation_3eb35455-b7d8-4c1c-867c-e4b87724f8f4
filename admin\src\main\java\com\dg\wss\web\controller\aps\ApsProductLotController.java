package com.dg.wss.web.controller.aps;

import com.dg.wss.aps.domain.ApsProductLot;
import com.dg.wss.aps.service.IApsProductLotService;
import com.dg.wss.common.annotation.Log;
import com.dg.wss.common.annotation.RepeatSubmit;
import com.dg.wss.common.core.domain.AjaxResult;
import com.dg.wss.common.core.page.TableDataInfo;
import com.dg.wss.common.enums.BusinessType;
import com.dg.wss.commonservice.BaseController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;
import java.util.Map;
@Api(tags = "aps 产品lot配置")
@RestController
@RequestMapping("/aps/apsProductLot")
public class ApsProductLotController extends BaseController {
    @Autowired
    private IApsProductLotService apsProductLotService;
    
    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
                 @ApiImplicitParam(paramType = "query", name = "id", value = "id", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "productId", value = "产品id", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "name", value = "名称", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "num", value = "数量", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "status", value = "状态 1首选 0默认", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createBy", value = "创建人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "createTime", value = "创建时间", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateBy", value = "更新人", dataType = "Sting"),
                 @ApiImplicitParam(paramType = "query", name = "updateTime", value = "更新时间", dataType = "Sting"),
    })
    @DynamicResponseParameters(name = "apsProductLot_list", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "total", value = "总记录数", example = "1000", required = true, dataTypeClass = Long.class),
            @DynamicParameter(name = "rows", value = "数据体", dataTypeClass = ApsProductLot[].class)
    })
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore @RequestParam Map<String, Object> map) {
        startPage();
        map.put("circleId",getCircleId());
        List<ApsProductLot> list = apsProductLotService.pageByMap(map);
        return getDataTable(list);
    }
    
    @ApiOperation(value = "添加单个")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsProductLot_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps 产品lot配置", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ApsProductLot apsProductLot) {
        apsProductLot.setCreateBy(getUserId());
        apsProductLot.setCreateTime(new Date());
        return (apsProductLotService.insert(apsProductLot));
    }
    
    //@ApiOperation(value = "添加多个")
    //@ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateBy", "updateTime"})
    //@DynamicResponseParameters(name = "collateralRate_add", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            //@DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    //@PreAuthorize("@ss.hasRole('basics')")
    //@RepeatSubmit
    //@PostMapping("/addlist")
    //public AjaxResult addlist(@RequestBody List<ApsProductLot> apsProductLots) {
        //return toAjax(apsProductLotService.batchInsert(apsProductLots, getUserId()));
    //}
    
    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"id", "createBy", "createTime", "updateBy", "updateTime"})
    @DynamicResponseParameters(name = "apsProductLot_update", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps 产品lot配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody ApsProductLot apsProductLot) {
        apsProductLot.setUpdateBy(getUserId());
        apsProductLot.setUpdateTime(new Date());
        return toAjax(apsProductLotService.update(apsProductLot));
    }

    @ApiOperation(value = "修改产品的首选lot")
    @PutMapping("/setDefault")
    public AjaxResult setDefault(@RequestBody ApsProductLot apsProductLot) {
        apsProductLot.setUpdateBy(getUserId());
        apsProductLot.setUpdateTime(new Date());
        return toAjax(apsProductLotService.setDefault(apsProductLot.getId()));
    }
    
    @ApiOperation(value = "查询by id")
    @DynamicResponseParameters(properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class),
            @DynamicParameter(name = "data", value = "数据体", dataTypeClass = ApsProductLot.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @GetMapping("/getInfo")
    public AjaxResult selectById(@ApiParam("ID") @RequestParam Long id) {
        return AjaxResult.success(apsProductLotService.selectById(id));
    }
    
    @ApiOperation(value = "删除")
    @DynamicResponseParameters(name = "apsProductLot_remove", properties = {@DynamicParameter(name = "code", value = "状态码", example = "200", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "msg", value = "消息", example = "操作成功", required = true, dataTypeClass = String.class)})
    @PreAuthorize("@ss.hasRole('basics')")
    @Log(title = "aps 产品lot配置", businessType = BusinessType.DELETE)
    @RepeatSubmit
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("ID") @PathVariable Long id) {
        return toAjax(apsProductLotService.delete(id));
    }
}